package com.mdsadrulhasan.appy99lisence.fragments;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.mdsadrulhasan.appy99lisence.R;
import com.mdsadrulhasan.appy99lisence.recharge.PermissionHelper;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeService;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity;

public class DashboardRechargeFragment extends Fragment {

    private static final String TAG = "DashboardRechargeFragment";

    // UI Components
    private TextView bankingStatusText;
    private Button testBankingButton;
    private ToggleButton bkashToggle;
    private ToggleButton nagadToggle;
    private ToggleButton rocketToggle;
    private ToggleButton autoBankingToggle;

    // Data from parent activity
    private String licenseKey;
    private String deviceId;
    private String domainUrl;

    private SharedPreferences preferences;

    public static DashboardRechargeFragment newInstance(String licenseKey, String deviceId, String domainUrl) {
        DashboardRechargeFragment fragment = new DashboardRechargeFragment();
        Bundle args = new Bundle();
        args.putString("license_key", licenseKey);
        args.putString("device_id", deviceId);
        args.putString("domain_url", domainUrl);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            licenseKey = getArguments().getString("license_key");
            deviceId = getArguments().getString("device_id");
            domainUrl = getArguments().getString("domain_url");
        }

        preferences = PreferenceManager.getDefaultSharedPreferences(requireContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_telecom_banking, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initializeViews(view);
        setupClickListeners();
        loadBankingSettings();
        updateBankingStatus();
    }

    private void initializeViews(View view) {
        bankingStatusText = view.findViewById(R.id.banking_status_text);
        testBankingButton = view.findViewById(R.id.test_banking_button);
        bkashToggle = view.findViewById(R.id.bkash_toggle);
        nagadToggle = view.findViewById(R.id.nagad_toggle);
        rocketToggle = view.findViewById(R.id.rocket_toggle);
        autoBankingToggle = view.findViewById(R.id.auto_banking_toggle);
    }

    private void setupClickListeners() {
        if (testBankingButton != null) {
            testBankingButton.setOnClickListener(v -> testBankingConnection());
        }

        if (bkashToggle != null) {
            bkashToggle.setOnCheckedChangeListener((buttonView, isChecked) ->
                saveBankingSetting("bkash_enabled", isChecked));
        }

        if (nagadToggle != null) {
            nagadToggle.setOnCheckedChangeListener((buttonView, isChecked) ->
                saveBankingSetting("nagad_enabled", isChecked));
        }

        if (rocketToggle != null) {
            rocketToggle.setOnCheckedChangeListener((buttonView, isChecked) ->
                saveBankingSetting("rocket_enabled", isChecked));
        }

        if (autoBankingToggle != null) {
            autoBankingToggle.setOnCheckedChangeListener((buttonView, isChecked) ->
                saveBankingSetting("auto_banking_enabled", isChecked));
        }
    }

    private void loadBankingSettings() {
        if (bkashToggle != null) {
            bkashToggle.setChecked(preferences.getBoolean("bkash_enabled", false));
        }
        if (nagadToggle != null) {
            nagadToggle.setChecked(preferences.getBoolean("nagad_enabled", false));
        }
        if (rocketToggle != null) {
            rocketToggle.setChecked(preferences.getBoolean("rocket_enabled", false));
        }
        if (autoBankingToggle != null) {
            autoBankingToggle.setChecked(preferences.getBoolean("auto_banking_enabled", true));
        }
    }

    private void saveBankingSetting(String key, boolean value) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(key, value);
        editor.apply();

        updateBankingStatus();
        Log.d(TAG, "Banking setting saved: " + key + " = " + value);
    }

    private void updateBankingStatus() {
        if (bankingStatusText != null) {
            boolean hasPermissions = PermissionHelper.hasAllRechargePermissions(requireContext());
            boolean serviceRunning = preferences.getBoolean("recharge_service_running", false);

            String status;
            if (!hasPermissions) {
                status = "⚠️ Permissions required for banking functionality";
                bankingStatusText.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            } else if (serviceRunning) {
                status = "🟢 Banking services are ready and running";
                bankingStatusText.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            } else {
                status = "🔴 Banking services are ready but stopped";
                bankingStatusText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }

            bankingStatusText.setText(status);
        }
    }

    private void testBankingConnection() {
        try {
            // Start recharge service to test connection
            RechargeService.startRechargeService(requireContext());

            Toast.makeText(requireContext(), "Testing banking connection...", Toast.LENGTH_SHORT).show();

            // Update status after a short delay
            bankingStatusText.postDelayed(() -> updateBankingStatus(), 2000);

        } catch (Exception e) {
            Log.e(TAG, "Error testing banking connection", e);
            Toast.makeText(requireContext(), "Error testing banking connection", Toast.LENGTH_SHORT).show();
        }
    }

    private void openRechargeActivity() {
        try {
            Intent intent = new Intent(requireContext(), RechargeActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            intent.putExtra("domain_url", domainUrl);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge activity", e);
            Toast.makeText(requireContext(), "Error opening recharge module", Toast.LENGTH_SHORT).show();
        }
    }

    private void openRechargeHistoryActivity() {
        try {
            Intent intent = new Intent(requireContext(), RechargeHistoryActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge history activity", e);
            Toast.makeText(requireContext(), "Error opening recharge history", Toast.LENGTH_SHORT).show();
        }
    }

    private void openRechargeSettingsActivity() {
        try {
            Intent intent = new Intent(requireContext(), RechargeSettingsActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge settings activity", e);
            Toast.makeText(requireContext(), "Error opening recharge settings", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh status when fragment becomes visible
        updateBankingStatus();
    }
}
