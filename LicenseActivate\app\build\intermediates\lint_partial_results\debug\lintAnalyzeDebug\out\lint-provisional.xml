<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="conditional_incidents">

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.READ_PHONE_STATE" requirement="android.permission.READ_PHONE_STATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="148"
            column="60"
            startOffset="5326"
            endLine="148"
            endColumn="111"
            endOffset="5377"/>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.READ_PHONE_STATE" requirement="android.permission.READ_PHONE_STATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="237"
            column="60"
            startOffset="8453"
            endLine="237"
            endColumn="111"
            endOffset="8504"/>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.READ_PHONE_STATE" requirement="android.permission.READ_PHONE_STATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="258"
            column="60"
            startOffset="9271"
            endLine="258"
            endColumn="111"
            endOffset="9322"/>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.READ_PHONE_STATE" requirement="android.permission.READ_PHONE_STATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="276"
            column="60"
            startOffset="9978"
            endLine="276"
            endColumn="111"
            endOffset="10029"/>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.READ_PHONE_STATE" requirement="android.permission.READ_PHONE_STATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
            line="88"
            column="54"
            startOffset="3371"
            endLine="88"
            endColumn="98"
            endOffset="3415"/>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.READ_PHONE_STATE" requirement="android.permission.READ_PHONE_STATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
            line="230"
            column="62"
            startOffset="8335"
            endLine="230"
            endColumn="106"
            endOffset="8379"/>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.READ_PHONE_STATE" requirement="android.permission.READ_PHONE_STATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
            line="253"
            column="62"
            startOffset="9240"
            endLine="253"
            endColumn="106"
            endOffset="9284"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1396"
            column="13"
            startOffset="51525"
            endLine="1396"
            endColumn="62"
            endOffset="51574"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1738"
            column="13"
            startOffset="64962"
            endLine="1738"
            endColumn="81"
            endOffset="65030"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1865"
            column="13"
            startOffset="69794"
            endLine="1865"
            endColumn="81"
            endOffset="69862"/>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="17"
            column="6"
            startOffset="781"
            endLine="17"
            endColumn="21"
            endOffset="796"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="19"
            column="6"
            startOffset="927"
            endLine="19"
            endColumn="21"
            endOffset="942"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="20"
            column="6"
            startOffset="998"
            endLine="20"
            endColumn="21"
            endOffset="1013"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="21"
            column="6"
            startOffset="1066"
            endLine="21"
            endColumn="21"
            endOffset="1081"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="32"
            column="6"
            startOffset="1756"
            endLine="32"
            endColumn="21"
            endOffset="1771"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="34"
            column="6"
            startOffset="1902"
            endLine="34"
            endColumn="21"
            endOffset="1917"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="35"
            column="6"
            startOffset="1973"
            endLine="35"
            endColumn="21"
            endOffset="1988"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="36"
            column="6"
            startOffset="2041"
            endLine="36"
            endColumn="21"
            endOffset="2056"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="41"
            column="6"
            startOffset="2341"
            endLine="41"
            endColumn="21"
            endOffset="2356"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="42"
            column="6"
            startOffset="2416"
            endLine="42"
            endColumn="21"
            endOffset="2431"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="3242"
                endOffset="3262"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="59"
            column="9"
            startOffset="3242"
            endLine="59"
            endColumn="29"
            endOffset="3262"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
