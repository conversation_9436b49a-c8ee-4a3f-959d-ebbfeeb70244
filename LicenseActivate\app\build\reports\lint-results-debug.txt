C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:148: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:237: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:258: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:276: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java:88: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
            List<PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java:230: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
                    List<PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();
                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java:253: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
                    List<PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();
                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingPermission":
   This check scans through your code and libraries and looks at the APIs
   being used, and checks this against the set of permissions required to
   access those APIs. If the code using those APIs is called at runtime, then
   the program will crash.

   Furthermore, for permissions that are revocable (with targetSdkVersion 23),
   client code must also be prepared to handle the calls throwing an exception
   if the user rejects the request for permission at runtime.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:266: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            switch (transaction.status.toLowerCase()) {
                                       ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:220: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            if (messageBody.toLowerCase().contains("failed") || 
                            ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:221: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                messageBody.toLowerCase().contains("error") ||
                            ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:222: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                messageBody.toLowerCase().contains("insufficient")) {
                            ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java:94: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String normalizedSender = sender.toLowerCase().trim();
                                         ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java:95: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String normalizedMessage = message.toLowerCase().trim();
                                           ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java:223: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String normalizedMessage = message.toLowerCase().trim();
                                           ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomLogAdapter.java:52: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (entry.getStatus().toLowerCase()) {
                                  ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomSmsReceiver.java:83: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerMessage = message.toLowerCase();
                                      ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomSmsReceiver.java:84: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerSender = sender.toLowerCase();
                                    ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomSmsReceiver.java:242: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerMessage = message.toLowerCase();
                                      ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:100: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("ussd") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:101: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("balance") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:102: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("recharge") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:103: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("tk") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:104: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("taka") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:105: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("successful") ||
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:106: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    dialogText.toLowerCase().contains("failed"))) {
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java:272: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerResponse = response.toLowerCase();
                                        ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:138: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    transaction.orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));
                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:139: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    transaction.phoneNumber = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_PHONE_NUMBER));
                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:140: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    transaction.amount = cursor.getDouble(cursor.getColumnIndex(RechargeDbHelper.COLUMN_AMOUNT));
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:141: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    transaction.operator = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_OPERATOR));
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:142: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    transaction.status = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_STATUS));
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:143: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    transaction.createdAt = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_CREATED_AT));
                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:144: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    transaction.smsResponse = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SMS_RESPONSE));
                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:145: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:146: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String ussdCode = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_USSD_CODE));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:147: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int simSlot = cursor.getInt(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SIM_SLOT));
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:185: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    long smsId = cursor.getLong(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ID));
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:186: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    String sender = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SENDER));
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:187: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    String messageBody = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_MESSAGE_BODY));
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java:188: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                    String orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));
                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java:283: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String dbId = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_ID));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java:284: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String orderId = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_ORDER_ID));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java:285: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String ussdCode = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_USSD));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java:286: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int slot = cursor.getInt(cursor.getColumnIndex(TelecomDbHelper.COLUMN_SLOT));
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Range":
   Some parameters are required to be in a particular numerical range; this
   check makes sure that arguments passed fall within the range. For arrays,
   Strings and collections this refers to the size or length.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:22: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:41: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:42: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ProtectedPermissions":
   Permissions with the protection level signature, privileged or
   signatureOrSystem are only granted to system apps (unless they also include
   appop). If an app is a regular non-system app, it will never be able to use
   these permissions.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.10.0 is available: 8.10.1 [AndroidGradlePluginVersion]
agp = "8.10.0"
      ~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:2: Warning: "Lisence" is a common misspelling; did you mean "License" or "Licence"? [Typos]
    <string name="app_name">Appy99Lisence</string>
                                  ^

   Explanation for issues of type "Typos":
   This check looks through the string definitions, and if it finds any words
   that look like likely misspellings, they are flagged.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.CALL_PHONE" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.READ_SMS" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.SEND_SMS" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:32: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.CALL_PHONE" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:34: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:35: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.READ_SMS" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:36: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.SEND_SMS" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:41: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
     ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:42: Error: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.telephony" required="false"> tag [PermissionImpliesUnsupportedChromeOsHardware]
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
     ~~~~~~~~~~~~~~~

   Explanation for issues of type "PermissionImpliesUnsupportedChromeOsHardware":
   The <uses-permission> element should not require a permission that implies
   an unsupported large screen hardware feature. Google Play assumes that
   certain hardware related permissions indicate that the underlying hardware
   features are required by default. To fix the issue, consider declaring the
   corresponding <uses-feature> element with required="false" attribute.

   https://developer.android.com/topic/arc/manifest.html#implied-features

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:861: Warning: Using getString to get device identifiers is not recommended [HardwareIds]
        return Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardwareIds":
   Using these device identifiers is not recommended other than for high value
   fraud prevention and advanced telephony use-cases. For advertising
   use-cases, use AdvertisingIdClient$Info#getId and for analytics, use
   InstanceId#getId.

   https://developer.android.com/training/articles/user-data-ids.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:117: Warning: BroadcastReceivers that declare an intent-filter for SMS_DELIVER or SMS_RECEIVED must ensure that the caller has the BROADCAST_SMS permission, otherwise it is possible for malicious actors to spoof intents [UnprotectedSMSBroadcastReceiver]
        <receiver
         ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:134: Warning: BroadcastReceivers that declare an intent-filter for SMS_DELIVER or SMS_RECEIVED must ensure that the caller has the BROADCAST_SMS permission, otherwise it is possible for malicious actors to spoof intents [UnprotectedSMSBroadcastReceiver]
        <receiver
         ~~~~~~~~

   Explanation for issues of type "UnprotectedSMSBroadcastReceiver":
   BroadcastReceivers that declare an intent-filter for SMS_DELIVER or
   SMS_RECEIVED must ensure that the caller has the BROADCAST_SMS permission,
   otherwise it is possible for malicious actors to spoof intents.

   https://goo.gle/UnprotectedSMSBroadcastReceiver

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:156: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                    historyAdapter.notifyDataSetChanged();
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java:55: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:34: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:55: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:84: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:100: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:126: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:147: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:236: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:257: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java:275: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java:47: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java:78: Warning: Unnecessary; SDK_INT is never < 24 [ObsoleteSdkInt]
            if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java:227: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java:250: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:8: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/Theme.Appy99Lisence) [Overdraw]
    android:background="#f5f5f5"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:7: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/Theme.Appy99Lisence) [Overdraw]
    android:background="#f5f5f5"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:7: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/Theme.Appy99Lisence) [Overdraw]
    android:background="#f5f5f5"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:7: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/Theme.Appy99Lisence) [Overdraw]
    android:background="#f5f5f5"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:2: Warning: The resource R.layout.activity_telecom_dashboard appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml:2: Warning: The resource R.menu.bottom_navigation_menu appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.waiting appears to be unused [UnusedResources]
    <color name="waiting">#FFAA00</color>
           ~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.done appears to be unused [UnusedResources]
    <color name="done">#00CC00</color>
           ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.colorPrimary appears to be unused [UnusedResources]
    <color name="colorPrimary">#6200EE</color>
           ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:10: Warning: The resource R.color.failed appears to be unused [UnusedResources]
    <color name="failed">#FF0000</color>
           ~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.primary_blue appears to be unused [UnusedResources]
    <color name="primary_blue">#3F51B5</color>
           ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.primary_blue_dark appears to be unused [UnusedResources]
    <color name="primary_blue_dark">#1A237E</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.primary_blue_light appears to be unused [UnusedResources]
    <color name="primary_blue_light">#3949AB</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:17: Warning: The resource R.color.background_light appears to be unused [UnusedResources]
    <color name="background_light">#EEEEEE</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:18: Warning: The resource R.color.card_background appears to be unused [UnusedResources]
    <color name="card_background">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:19: Warning: The resource R.color.text_primary appears to be unused [UnusedResources]
    <color name="text_primary">#212121</color>
           ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml:21: Warning: The resource R.color.text_hint appears to be unused [UnusedResources]
    <color name="text_hint">#9E9E9E</color>
           ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\domain_button_background.xml:2: Warning: The resource R.drawable.domain_button_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\edit_text_background.xml:2: Warning: The resource R.drawable.edit_text_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\menu_main.xml:2: Warning: The resource R.menu.menu_main appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\nav_item_background.xml:2: Warning: The resource R.drawable.nav_item_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\rounded_corner.xml:2: Warning: The resource R.drawable.rounded_corner appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\status_background.xml:2: Warning: The resource R.drawable.status_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.telecom_dashboard_title appears to be unused [UnusedResources]
    <string name="telecom_dashboard_title">Telecom Recharge System</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.telecom_home_tab appears to be unused [UnusedResources]
    <string name="telecom_home_tab">Home</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.telecom_banking_tab appears to be unused [UnusedResources]
    <string name="telecom_banking_tab">Banking</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.telecom_settings_tab appears to be unused [UnusedResources]
    <string name="telecom_settings_tab">Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.telecom_monitor_tab appears to be unused [UnusedResources]
    <string name="telecom_monitor_tab">Monitor</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:13: Warning: The resource R.string.service_status_running appears to be unused [UnusedResources]
    <string name="service_status_running">Service Status: Running</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:14: Warning: The resource R.string.service_status_stopped appears to be unused [UnusedResources]
    <string name="service_status_stopped">Service Status: Stopped</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.service_status_starting appears to be unused [UnusedResources]
    <string name="service_status_starting">Service Status: Starting...</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.service_status_stopping appears to be unused [UnusedResources]
    <string name="service_status_stopping">Service Status: Stopping...</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.start_service appears to be unused [UnusedResources]
    <string name="start_service">Start Service</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.stop_service appears to be unused [UnusedResources]
    <string name="stop_service">Stop Service</string>
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.refresh appears to be unused [UnusedResources]
    <string name="refresh">Refresh</string>
            ~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.clear_logs appears to be unused [UnusedResources]
    <string name="clear_logs">Clear Logs</string>
            ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.export_logs appears to be unused [UnusedResources]
    <string name="export_logs">Export</string>
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.sms_service appears to be unused [UnusedResources]
    <string name="sms_service">SMS Service</string>
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:27: Warning: The resource R.string.accessibility_service appears to be unused [UnusedResources]
    <string name="accessibility_service">Accessibility</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.power_optimize appears to be unused [UnusedResources]
    <string name="power_optimize">Power Optimize</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.auto_start_service appears to be unused [UnusedResources]
    <string name="auto_start_service">Auto Start Service</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.network_settings appears to be unused [UnusedResources]
    <string name="network_settings">Network Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:31: Warning: The resource R.string.system_settings appears to be unused [UnusedResources]
    <string name="system_settings">System Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:34: Warning: The resource R.string.mobile_banking_services appears to be unused [UnusedResources]
    <string name="mobile_banking_services">Mobile Banking Services</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:35: Warning: The resource R.string.banking_configuration appears to be unused [UnusedResources]
    <string name="banking_configuration">Banking Configuration</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:36: Warning: The resource R.string.auto_banking appears to be unused [UnusedResources]
    <string name="auto_banking">Auto Banking</string>
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:37: Warning: The resource R.string.banking_delay appears to be unused [UnusedResources]
    <string name="banking_delay">Banking Delay (seconds)</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:38: Warning: The resource R.string.banking_status appears to be unused [UnusedResources]
    <string name="banking_status">Banking Status</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.test_banking_connection appears to be unused [UnusedResources]
    <string name="test_banking_connection">Test Banking Connection</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.monitor_controls appears to be unused [UnusedResources]
    <string name="monitor_controls">Monitor Controls</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.live_monitor appears to be unused [UnusedResources]
    <string name="live_monitor">Live Monitor</string>
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.auto_scroll appears to be unused [UnusedResources]
    <string name="auto_scroll">Auto Scroll</string>
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.no_logs_available appears to be unused [UnusedResources]
    <string name="no_logs_available">No logs available</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:46: Warning: The resource R.string.no_recent_activity appears to be unused [UnusedResources]
    <string name="no_recent_activity">No recent activity</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:49: Warning: The resource R.string.total_recharges appears to be unused [UnusedResources]
    <string name="total_recharges">Total Recharges</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:50: Warning: The resource R.string.success_rate appears to be unused [UnusedResources]
    <string name="success_rate">Success Rate</string>
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:51: Warning: The resource R.string.recent_activity appears to be unused [UnusedResources]
    <string name="recent_activity">Recent Activity</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:54: Warning: The resource R.string.telecom_service_started appears to be unused [UnusedResources]
    <string name="telecom_service_started">Telecom service started</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:55: Warning: The resource R.string.telecom_service_stopped appears to be unused [UnusedResources]
    <string name="telecom_service_stopped">Telecom service stopped</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:56: Warning: The resource R.string.error_starting_service appears to be unused [UnusedResources]
    <string name="error_starting_service">Error starting telecom service</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:57: Warning: The resource R.string.error_stopping_service appears to be unused [UnusedResources]
    <string name="error_stopping_service">Error stopping telecom service</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\telecom_card_background.xml:2: Warning: The resource R.drawable.telecom_card_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\toggle_off.xml:2: Warning: The resource R.drawable.toggle_off appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\toggle_on.xml:2: Warning: The resource R.drawable.toggle_on appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\toggle_selector.xml:2: Warning: The resource R.drawable.toggle_selector appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:15: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="service_status_starting">Service Status: Starting...</string>
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml:16: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="service_status_stopping">Service Status: Stopping...</string>
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:247: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                            <Button
                             ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:260: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                            <Button
                             ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:274: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                            <Button
                             ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:242: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:255: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:320: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:333: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:239: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                    <Button
                     ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:250: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                    <Button
                     ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:38: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:48: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:59: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:51: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                    <Button
                     ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:62: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                    <Button
                     ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:81: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:123: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:85: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:106: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:157: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:177: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:197: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:192: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle:37: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.cardview:cardview:1.0.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle:40: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle:41: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.preference:preference:1.2.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle:42: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle:43: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle:44: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:336: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:369: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:401: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:433: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:37: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:70: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:102: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:134: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:172: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:205: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        welcomeText.setText("🎉 Welcome to Your Dashboard!");
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:209: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        licenseInfoText.setText("📄 License: " + maskedLicense);
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:209: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        licenseInfoText.setText("📄 License: " + maskedLicense);
                                ~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:212: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        domainInfoText.setText("🌐 Domain: " + domainUrl);
                               ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:212: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        domainInfoText.setText("🌐 Domain: " + domainUrl);
                               ~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:221: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                expirationInfoText.setText("⏰ Expires: " + expirationDate + "\n" +
                                           ^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:221: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                expirationInfoText.setText("⏰ Expires: " + expirationDate + "\n" +
                                           ~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:222: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                                         "⏳ Time remaining: " + timeRemainingStr);
                                         ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:224: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                expirationInfoText.setText("⚠️ License has expired on: " + expirationDate);
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:224: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                expirationInfoText.setText("⚠️ License has expired on: " + expirationDate);
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:228: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            expirationInfoText.setText("⏰ Expiration: Not available");
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:233: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            deviceInfoText.setText("📱 Device: " + deviceInfo + "\n" +
                                   ^
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:233: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            deviceInfoText.setText("📱 Device: " + deviceInfo + "\n" +
                                   ~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:234: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                                 "🔑 Device ID: " + (deviceId != null ? deviceId.substring(0, Math.min(deviceId.length(), 8)) + "..." : "N/A"));
                                 ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:236: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            deviceInfoText.setText("📱 Device information not available");
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:249: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        welcomeText.setText("❌ Authentication Error");
                            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:250: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        licenseInfoText.setText("Invalid license or authentication data received.");
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:251: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        domainInfoText.setText("Please return to the main screen and try again.");
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:661: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                            serviceStatusText.setText("Service Status: Starting...");
                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:673: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                            serviceStatusText.setText("Service Status: Stopped");
                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:683: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                serviceStatusText.setText("Service Status: " + (serviceRunning ? "Running" : "Stopped"));
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java:683: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                serviceStatusText.setText("Service Status: " + (serviceRunning ? "Running" : "Stopped"));
                                          ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:188: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            statusText.setText("App is activated and running");
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:195: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                statusText.setText("Approval pending... Checking status...");
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:198: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                statusText.setText("Please enter your license key and PIN to activate");
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:226: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        statusText.setText("Authenticating...");
                           ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:396: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            statusText.setText("App activated successfully!");
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:474: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        statusText.setText("Authentication failed");
                           ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:520: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        statusText.setText("Approval check timeout. Please try again later.");
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:619: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    statusText.setText("Error checking approval status");
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:658: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    statusText.setText("Still pending approval... (" + approvalCheckCount + "/" + MAX_APPROVAL_CHECKS + ")");
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:658: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    statusText.setText("Still pending approval... (" + approvalCheckCount + "/" + MAX_APPROVAL_CHECKS + ")");
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:836: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        activateButton.setText("Activated");
                               ~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:919: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            statusText.setText("Resuming approval status check...");
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:969: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            domainInfoText.setText("Your domain: " + domain);
                                   ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:969: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            domainInfoText.setText("Your domain: " + domain);
                                   ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1024: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        domainLoginButton.setText("🔄 Opening...");
                                                  ~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1026: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        domainLoginButton.setText("🎛️ Open Dashboard");
                                                  ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1558: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                expirationText.setText("License Expired");
                                       ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1562: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                countdownText.setText("Please renew your license");
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1583: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            expirationText.setText("License expires: " + expirationDate);
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1583: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            expirationText.setText("License expires: " + expirationDate);
                                   ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1932: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    activateButton.setText("Activate License");
                                           ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java:1937: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    statusText.setText("License expired. Please enter a new license key to continue.");
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:257: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            holder.orderIdText.setText("Order: " + transaction.orderId);
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:257: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            holder.orderIdText.setText("Order: " + transaction.orderId);
                                       ~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:258: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            holder.phoneNumberText.setText("📞 " + transaction.phoneNumber);
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:259: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            holder.amountText.setText("💰 " + transaction.amount + " BDT");
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:259: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            holder.amountText.setText("💰 " + transaction.amount + " BDT");
                                                                   ~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:260: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            holder.operatorText.setText("📡 " + transaction.operator);
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:261: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            holder.statusText.setText("Status: " + transaction.status);
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:261: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            holder.statusText.setText("Status: " + transaction.status);
                                      ~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java:262: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            holder.dateText.setText("📅 " + transaction.createdAt);
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:37: Warning: Hardcoded string "🎉 Welcome to Your Dashboard!", should use @string resource [HardcodedText]
                    android:text="🎉 Welcome to Your Dashboard!"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:47: Warning: Hardcoded string "Your license and domain information", should use @string resource [HardcodedText]
                    android:text="Your license and domain information"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:71: Warning: Hardcoded string "📄 License Information", should use @string resource [HardcodedText]
                        android:text="📄 License Information"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:81: Warning: Hardcoded string "License: Loading...", should use @string resource [HardcodedText]
                        android:text="License: Loading..."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:107: Warning: Hardcoded string "🌐 Domain Access", should use @string resource [HardcodedText]
                        android:text="🌐 Domain Access"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:117: Warning: Hardcoded string "Domain: Loading...", should use @string resource [HardcodedText]
                        android:text="Domain: Loading..."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:143: Warning: Hardcoded string "⏰ License Expiration", should use @string resource [HardcodedText]
                        android:text="⏰ License Expiration"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:153: Warning: Hardcoded string "Expiration: Loading...", should use @string resource [HardcodedText]
                        android:text="Expiration: Loading..."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:179: Warning: Hardcoded string "📱 Device Information", should use @string resource [HardcodedText]
                        android:text="📱 Device Information"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:189: Warning: Hardcoded string "Device: Loading...", should use @string resource [HardcodedText]
                        android:text="Device: Loading..."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:225: Warning: Hardcoded string "📱 Mobile Recharge Module", should use @string resource [HardcodedText]
                            android:text="📱 Mobile Recharge Module"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:235: Warning: Hardcoded string "🔴 Recharge service is stopped", should use @string resource [HardcodedText]
                            android:text="🔴 Recharge service is stopped"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:253: Warning: Hardcoded string "💳 Recharge", should use @string resource [HardcodedText]
                                android:text="💳 Recharge"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:267: Warning: Hardcoded string "📊 History", should use @string resource [HardcodedText]
                                android:text="📊 History"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:280: Warning: Hardcoded string "⚙️ Settings", should use @string resource [HardcodedText]
                                android:text="⚙️ Settings"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:346: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
                                android:text="Home"
                                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:379: Warning: Hardcoded string "Banking", should use @string resource [HardcodedText]
                                android:text="Banking"
                                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:411: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
                                android:text="Settings"
                                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:443: Warning: Hardcoded string "Monitor", should use @string resource [HardcodedText]
                                android:text="Monitor"
                                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:464: Warning: Hardcoded string "🔙 Back to Main", should use @string resource [HardcodedText]
                android:text="🔙 Back to Main"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml:487: Warning: Hardcoded string "🎛️ Dashboard - License Management System", should use @string resource [HardcodedText]
            android:text="🎛️ Dashboard - License Management System"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:38: Warning: Hardcoded string "License Activation", should use @string resource [HardcodedText]
                    android:text="License Activation"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:48: Warning: Hardcoded string "Enter your license key and PIN to activate", should use @string resource [HardcodedText]
                    android:text="Enter your license key and PIN to activate"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:74: Warning: Hardcoded string "License Key", should use @string resource [HardcodedText]
                        android:text="License Key"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:85: Warning: Hardcoded string "Enter your license key", should use @string resource [HardcodedText]
                        android:hint="Enter your license key"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:116: Warning: Hardcoded string "PIN", should use @string resource [HardcodedText]
                        android:text="PIN"
                        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:127: Warning: Hardcoded string "Enter your PIN", should use @string resource [HardcodedText]
                        android:hint="Enter your PIN"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:145: Warning: Hardcoded string "Activate License", should use @string resource [HardcodedText]
                android:text="Activate License"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:160: Warning: Hardcoded string "Please enter your license key and PIN to activate", should use @string resource [HardcodedText]
                android:text="Please enter your license key and PIN to activate"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:202: Warning: Hardcoded string "🌐", should use @string resource [HardcodedText]
                                android:text="🌐"
                                ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:209: Warning: Hardcoded string "Domain Access", should use @string resource [HardcodedText]
                                android:text="Domain Access"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:221: Warning: Hardcoded string "Your domain: Loading...", should use @string resource [HardcodedText]
                            android:text="Your domain: Loading..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:231: Warning: Hardcoded string "🎛️ Open Dashboard", should use @string resource [HardcodedText]
                            android:text="🎛️ Open Dashboard"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:244: Warning: Hardcoded string "💡 View your license details and domain information", should use @string resource [HardcodedText]
                            android:text="💡 View your license details and domain information"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:288: Warning: Hardcoded string "📅", should use @string resource [HardcodedText]
                                android:text="📅"
                                ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:295: Warning: Hardcoded string "License Status", should use @string resource [HardcodedText]
                                android:text="License Status"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:307: Warning: Hardcoded string "License expires: Loading...", should use @string resource [HardcodedText]
                            android:text="License expires: Loading..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:317: Warning: Hardcoded string "Time remaining: Calculating...", should use @string resource [HardcodedText]
                            android:text="Time remaining: Calculating..."
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:337: Warning: Hardcoded string "💡 You will receive warnings 2 days before expiration", should use @string resource [HardcodedText]
                            android:text="💡 You will receive warnings 2 days before expiration"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:374: Warning: Hardcoded string "👨‍💻", should use @string resource [HardcodedText]
                android:text="👨‍💻"
                ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:381: Warning: Hardcoded string "Md Sadrul Hasan Dider", should use @string resource [HardcodedText]
                android:text="Md Sadrul Hasan Dider"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml:392: Warning: Hardcoded string "© 2025 License Activation System", should use @string resource [HardcodedText]
            android:text="© 2025 License Activation System"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:35: Warning: Hardcoded string "📱 Mobile Recharge", should use @string resource [HardcodedText]
                    android:text="📱 Mobile Recharge"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:45: Warning: Hardcoded string "Submit mobile recharge requests", should use @string resource [HardcodedText]
                    android:text="Submit mobile recharge requests"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:69: Warning: Hardcoded string "💳 Recharge Details", should use @string resource [HardcodedText]
                        android:text="💳 Recharge Details"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:79: Warning: Hardcoded string "📞 Phone Number", should use @string resource [HardcodedText]
                        android:text="📞 Phone Number"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:90: Warning: Hardcoded string "Enter phone number", should use @string resource [HardcodedText]
                        android:hint="Enter phone number"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:100: Warning: Hardcoded string "💰 Amount (BDT)", should use @string resource [HardcodedText]
                        android:text="💰 Amount (BDT)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:111: Warning: Hardcoded string "Enter amount", should use @string resource [HardcodedText]
                        android:hint="Enter amount"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:121: Warning: Hardcoded string "📡 Select Operator", should use @string resource [HardcodedText]
                        android:text="📡 Select Operator"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:139: Warning: Hardcoded string "📱 Select SIM Slot", should use @string resource [HardcodedText]
                        android:text="📱 Select SIM Slot"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:158: Warning: Hardcoded string "🚀 Submit Recharge", should use @string resource [HardcodedText]
                        android:text="🚀 Submit Recharge"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:186: Warning: Hardcoded string "📊 Status", should use @string resource [HardcodedText]
                        android:text="📊 Status"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:196: Warning: Hardcoded string "Ready to submit recharge", should use @string resource [HardcodedText]
                        android:text="Ready to submit recharge"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:224: Warning: Hardcoded string "ℹ️ Instructions", should use @string resource [HardcodedText]
                        android:text="ℹ️ Instructions"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:233: Warning: Hardcoded string "1. Enter the phone number to rechargen2. Enter the recharge amountn3. Select the mobile operatorn4. Choose the SIM slot to usen5. Click Submit to process the rechargennThe app will automatically dial the USSD code for you.", should use @string resource [HardcodedText]
                        android:text="1. Enter the phone number to recharge\n2. Enter the recharge amount\n3. Select the mobile operator\n4. Choose the SIM slot to use\n5. Click Submit to process the recharge\n\nThe app will automatically dial the USSD code for you."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:247: Warning: Hardcoded string "🔙 Back to Dashboard", should use @string resource [HardcodedText]
                android:text="🔙 Back to Dashboard"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml:270: Warning: Hardcoded string "📱 Mobile Recharge Module - License Management System", should use @string resource [HardcodedText]
            android:text="📱 Mobile Recharge Module - License Management System"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:23: Warning: Hardcoded string "📊 Recharge History", should use @string resource [HardcodedText]
            android:text="📊 Recharge History"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:32: Warning: Hardcoded string "🔄 Refresh", should use @string resource [HardcodedText]
            android:text="🔄 Refresh"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:45: Warning: Hardcoded string "Loading recharge history...", should use @string resource [HardcodedText]
        android:text="Loading recharge history..."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:76: Warning: Hardcoded string "📭", should use @string resource [HardcodedText]
            android:text="📭"
            ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:83: Warning: Hardcoded string "No recharge history found", should use @string resource [HardcodedText]
            android:text="No recharge history found"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:92: Warning: Hardcoded string "Your recharge transactions will appear here", should use @string resource [HardcodedText]
            android:text="Your recharge transactions will appear here"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml:111: Warning: Hardcoded string "🔙 Back to Dashboard", should use @string resource [HardcodedText]
            android:text="🔙 Back to Dashboard"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:35: Warning: Hardcoded string "⚙️ Recharge Settings", should use @string resource [HardcodedText]
                    android:text="⚙️ Recharge Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:45: Warning: Hardcoded string "Configure your recharge preferences", should use @string resource [HardcodedText]
                    android:text="Configure your recharge preferences"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:69: Warning: Hardcoded string "📱 SIM Configuration", should use @string resource [HardcodedText]
                        android:text="📱 SIM Configuration"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:79: Warning: Hardcoded string "📡 SIM 1 Operator", should use @string resource [HardcodedText]
                        android:text="📡 SIM 1 Operator"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:97: Warning: Hardcoded string "📡 SIM 2 Operator", should use @string resource [HardcodedText]
                        android:text="📡 SIM 2 Operator"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:116: Warning: Hardcoded string "🔄 Enable automatic recharge processing", should use @string resource [HardcodedText]
                        android:text="🔄 Enable automatic recharge processing"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:141: Warning: Hardcoded string "🌐 Server Configuration", should use @string resource [HardcodedText]
                        android:text="🌐 Server Configuration"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:151: Warning: Hardcoded string "🔗 Server URL", should use @string resource [HardcodedText]
                        android:text="🔗 Server URL"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:162: Warning: Hardcoded string "Enter server URL", should use @string resource [HardcodedText]
                        android:hint="Enter server URL"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:171: Warning: Hardcoded string "🔑 API PIN", should use @string resource [HardcodedText]
                        android:text="🔑 API PIN"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:182: Warning: Hardcoded string "Enter API PIN", should use @string resource [HardcodedText]
                        android:hint="Enter API PIN"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:191: Warning: Hardcoded string "⏱️ Check Interval (seconds)", should use @string resource [HardcodedText]
                        android:text="⏱️ Check Interval (seconds)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:202: Warning: Hardcoded string "Check interval in seconds", should use @string resource [HardcodedText]
                        android:hint="Check interval in seconds"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:204: Warning: Hardcoded string "5", should use @string resource [HardcodedText]
                        android:text="5"
                        ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:229: Warning: Hardcoded string "🎛️ Service Control", should use @string resource [HardcodedText]
                        android:text="🎛️ Service Control"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:248: Warning: Hardcoded string "▶️ Start Service", should use @string resource [HardcodedText]
                            android:text="▶️ Start Service"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:261: Warning: Hardcoded string "⏹️ Stop Service", should use @string resource [HardcodedText]
                            android:text="⏹️ Stop Service"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:291: Warning: Hardcoded string "📊 Status", should use @string resource [HardcodedText]
                        android:text="📊 Status"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:301: Warning: Hardcoded string "Loading settings...", should use @string resource [HardcodedText]
                        android:text="Loading settings..."
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:326: Warning: Hardcoded string "💾 Save Settings", should use @string resource [HardcodedText]
                    android:text="💾 Save Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:339: Warning: Hardcoded string "🔙 Back", should use @string resource [HardcodedText]
                    android:text="🔙 Back"
                    ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml:364: Warning: Hardcoded string "⚙️ Recharge Settings - License Management System", should use @string resource [HardcodedText]
            android:text="⚙️ Recharge Settings - License Management System"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:47: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
                android:text="Home"
                ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:80: Warning: Hardcoded string "Banking", should use @string resource [HardcodedText]
                android:text="Banking"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:112: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
                android:text="Settings"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:144: Warning: Hardcoded string "Monitor", should use @string resource [HardcodedText]
                android:text="Monitor"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:185: Warning: Hardcoded string "Automatic Mobile Recharge System", should use @string resource [HardcodedText]
                    android:text="Automatic Mobile Recharge System"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:199: Warning: Hardcoded string "Device ID: Loading...", should use @string resource [HardcodedText]
                    android:text="Device ID: Loading..."
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:219: Warning: Hardcoded string "Service Status", should use @string resource [HardcodedText]
                    android:text="Service Status"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:229: Warning: Hardcoded string "Service Status: Checking...", should use @string resource [HardcodedText]
                    android:text="Service Status: Checking..."
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:246: Warning: Hardcoded string "Start Service", should use @string resource [HardcodedText]
                        android:text="Start Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:257: Warning: Hardcoded string "Stop Service", should use @string resource [HardcodedText]
                        android:text="Stop Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:277: Warning: Hardcoded string "Network Settings", should use @string resource [HardcodedText]
                    android:text="Network Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:295: Warning: Hardcoded string "SIM A:", should use @string resource [HardcodedText]
                        android:text="SIM A:"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:321: Warning: Hardcoded string "SIM B:", should use @string resource [HardcodedText]
                        android:text="SIM B:"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:350: Warning: Hardcoded string "System Settings", should use @string resource [HardcodedText]
                    android:text="System Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:368: Warning: Hardcoded string "SMS Service", should use @string resource [HardcodedText]
                        android:text="SMS Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:398: Warning: Hardcoded string "Accessibility", should use @string resource [HardcodedText]
                        android:text="Accessibility"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml:427: Warning: Hardcoded string "Power Optimize", should use @string resource [HardcodedText]
                        android:text="Power Optimize"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml:6: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
        android:title="Home" />
        ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml:10: Warning: Hardcoded string "M Banking", should use @string resource [HardcodedText]
        android:title="M Banking" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml:14: Warning: Hardcoded string "Settings", should use @string resource [HardcodedText]
        android:title="Settings" />
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml:18: Warning: Hardcoded string "Monitor", should use @string resource [HardcodedText]
        android:title="Monitor" />
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:30: Warning: Hardcoded string "Mobile Banking Services", should use @string resource [HardcodedText]
                    android:text="Mobile Banking Services"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:49: Warning: Hardcoded string "bKash", should use @string resource [HardcodedText]
                        android:text="bKash"
                        ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:78: Warning: Hardcoded string "Rocket", should use @string resource [HardcodedText]
                        android:text="Rocket"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:106: Warning: Hardcoded string "Nagad", should use @string resource [HardcodedText]
                        android:text="Nagad"
                        ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:142: Warning: Hardcoded string "Banking Configuration", should use @string resource [HardcodedText]
                    android:text="Banking Configuration"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:161: Warning: Hardcoded string "Auto Banking", should use @string resource [HardcodedText]
                        android:text="Auto Banking"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:187: Warning: Hardcoded string "Banking Delay (seconds)", should use @string resource [HardcodedText]
                        android:text="Banking Delay (seconds)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:197: Warning: Hardcoded string "Enter delay in seconds", should use @string resource [HardcodedText]
                        android:hint="Enter delay in seconds"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:200: Warning: Hardcoded string "5", should use @string resource [HardcodedText]
                        android:text="5" />
                        ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:222: Warning: Hardcoded string "Banking Status", should use @string resource [HardcodedText]
                    android:text="Banking Status"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:232: Warning: Hardcoded string "Banking services are ready", should use @string resource [HardcodedText]
                    android:text="Banking services are ready"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml:241: Warning: Hardcoded string "Test Banking Connection", should use @string resource [HardcodedText]
                    android:text="Test Banking Connection"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml:36: Warning: Hardcoded string "Total Recharges", should use @string resource [HardcodedText]
                    android:text="Total Recharges"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml:45: Warning: Hardcoded string "0", should use @string resource [HardcodedText]
                    android:text="0"
                    ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml:72: Warning: Hardcoded string "Success Rate", should use @string resource [HardcodedText]
                    android:text="Success Rate"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml:81: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
                    android:text="0%"
                    ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml:107: Warning: Hardcoded string "Recent Activity", should use @string resource [HardcodedText]
                android:text="Recent Activity"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml:122: Warning: Hardcoded string "No recent activity", should use @string resource [HardcodedText]
                android:text="No recent activity"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:26: Warning: Hardcoded string "Monitor Controls", should use @string resource [HardcodedText]
                android:text="Monitor Controls"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:44: Warning: Hardcoded string "Refresh", should use @string resource [HardcodedText]
                    android:text="Refresh"
                    ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:55: Warning: Hardcoded string "Clear Logs", should use @string resource [HardcodedText]
                    android:text="Clear Logs"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:65: Warning: Hardcoded string "Export", should use @string resource [HardcodedText]
                    android:text="Export"
                    ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:97: Warning: Hardcoded string "Live Monitor", should use @string resource [HardcodedText]
                    android:text="Live Monitor"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:106: Warning: Hardcoded string "🟢 Active", should use @string resource [HardcodedText]
                    android:text="🟢 Active"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:140: Warning: Hardcoded string "Auto Scroll", should use @string resource [HardcodedText]
                    android:text="Auto Scroll"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml:161: Warning: Hardcoded string "No logs available", should use @string resource [HardcodedText]
                android:text="No logs available"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:30: Warning: Hardcoded string "Service Status", should use @string resource [HardcodedText]
                    android:text="Service Status"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:40: Warning: Hardcoded string "Service Status: Checking...", should use @string resource [HardcodedText]
                    android:text="Service Status: Checking..."
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:58: Warning: Hardcoded string "Start Service", should use @string resource [HardcodedText]
                        android:text="Start Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:69: Warning: Hardcoded string "Stop Service", should use @string resource [HardcodedText]
                        android:text="Stop Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:94: Warning: Hardcoded string "Network Settings", should use @string resource [HardcodedText]
                    android:text="Network Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:113: Warning: Hardcoded string "SIM A:", should use @string resource [HardcodedText]
                        android:text="SIM A:"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:137: Warning: Hardcoded string "SIM B:", should use @string resource [HardcodedText]
                        android:text="SIM B:"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:168: Warning: Hardcoded string "System Settings", should use @string resource [HardcodedText]
                    android:text="System Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:188: Warning: Hardcoded string "SMS Service", should use @string resource [HardcodedText]
                        android:text="SMS Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:217: Warning: Hardcoded string "Accessibility", should use @string resource [HardcodedText]
                        android:text="Accessibility"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:246: Warning: Hardcoded string "Power Optimize", should use @string resource [HardcodedText]
                        android:text="Power Optimize"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml:274: Warning: Hardcoded string "Auto Start Service", should use @string resource [HardcodedText]
                        android:text="Auto Start Service"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml:28: Warning: Hardcoded string "Order: RCH_123456", should use @string resource [HardcodedText]
                android:text="Order: RCH_123456"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml:37: Warning: Hardcoded string "Completed", should use @string resource [HardcodedText]
                android:text="Completed"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml:62: Warning: Hardcoded string "📞 +8801712345678", should use @string resource [HardcodedText]
                android:text="📞 +8801712345678"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml:70: Warning: Hardcoded string "💰 50 BDT", should use @string resource [HardcodedText]
                android:text="💰 50 BDT"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml:88: Warning: Hardcoded string "📡 Grameenphone", should use @string resource [HardcodedText]
                android:text="📡 Grameenphone"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml:96: Warning: Hardcoded string "📅 2024-01-15 10:30", should use @string resource [HardcodedText]
                android:text="📅 2024-01-15 10:30"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml:107: Warning: Hardcoded string "SMS: Recharge successful", should use @string resource [HardcodedText]
            android:text="SMS: Recharge successful"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_telecom_log.xml:14: Warning: Hardcoded string "12:34:56", should use @string resource [HardcodedText]
        android:text="12:34:56"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_telecom_log.xml:25: Warning: Hardcoded string "Log message", should use @string resource [HardcodedText]
        android:text="Log message"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_telecom_log.xml:34: Warning: Hardcoded string "Status", should use @string resource [HardcodedText]
        android:text="Status"
        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\menu_main.xml:6: Warning: Hardcoded string "Mobile Banking", should use @string resource [HardcodedText]
        android:title="Mobile Banking"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\menu_main.xml:11: Warning: Hardcoded string "Forward", should use @string resource [HardcodedText]
        android:title="Forward"
        ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

38 errors, 356 warnings
