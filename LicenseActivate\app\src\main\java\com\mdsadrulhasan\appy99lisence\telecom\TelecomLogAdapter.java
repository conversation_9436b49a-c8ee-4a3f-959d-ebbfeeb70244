package com.mdsadrulhasan.appy99lisence.telecom;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.appy99lisence.R;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for displaying telecom logs in RecyclerView
 */
public class TelecomLogAdapter extends RecyclerView.Adapter<TelecomLogAdapter.LogViewHolder> {
    
    private Context context;
    private List<TelecomLogEntry> logEntries;
    private SimpleDateFormat dateFormat;
    
    public TelecomLogAdapter(Context context) {
        this.context = context;
        this.logEntries = new ArrayList<>();
        this.dateFormat = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
    }
    
    @NonNull
    @Override
    public LogViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_telecom_log, parent, false);
        return new LogViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull LogViewHolder holder, int position) {
        TelecomLogEntry entry = logEntries.get(position);
        
        holder.timeText.setText(dateFormat.format(new Date(entry.getTimestamp())));
        holder.messageText.setText(entry.getMessage());
        holder.statusText.setText(entry.getStatus());
        
        // Set status color based on type
        int statusColor;
        switch (entry.getStatus().toLowerCase()) {
            case "completed":
            case "success":
                statusColor = context.getResources().getColor(R.color.success_green);
                break;
            case "failed":
            case "error":
                statusColor = context.getResources().getColor(R.color.error_red);
                break;
            case "processing":
            case "pending":
                statusColor = context.getResources().getColor(R.color.warning_orange);
                break;
            default:
                statusColor = context.getResources().getColor(R.color.text_secondary);
                break;
        }
        
        holder.statusText.setTextColor(statusColor);
    }
    
    @Override
    public int getItemCount() {
        return logEntries.size();
    }
    
    /**
     * Add new log entry
     */
    public void addLogEntry(TelecomLogEntry entry) {
        logEntries.add(0, entry); // Add to top
        notifyItemInserted(0);
        
        // Limit to 100 entries to prevent memory issues
        if (logEntries.size() > 100) {
            logEntries.remove(logEntries.size() - 1);
            notifyItemRemoved(logEntries.size());
        }
    }
    
    /**
     * Add new log entry with current timestamp
     */
    public void addLogEntry(String message, String status) {
        addLogEntry(new TelecomLogEntry(message, status, System.currentTimeMillis()));
    }
    
    /**
     * Clear all log entries
     */
    public void clearLogs() {
        int size = logEntries.size();
        logEntries.clear();
        notifyItemRangeRemoved(0, size);
    }
    
    /**
     * Get all log entries
     */
    public List<TelecomLogEntry> getLogEntries() {
        return new ArrayList<>(logEntries);
    }
    
    /**
     * ViewHolder class for log entries
     */
    static class LogViewHolder extends RecyclerView.ViewHolder {
        TextView timeText;
        TextView messageText;
        TextView statusText;
        
        LogViewHolder(@NonNull View itemView) {
            super(itemView);
            timeText = itemView.findViewById(R.id.log_time_text);
            messageText = itemView.findViewById(R.id.log_message_text);
            statusText = itemView.findViewById(R.id.log_status_text);
        }
    }
    
    /**
     * Log entry data class
     */
    public static class TelecomLogEntry {
        private String message;
        private String status;
        private long timestamp;
        
        public TelecomLogEntry(String message, String status, long timestamp) {
            this.message = message;
            this.status = status;
            this.timestamp = timestamp;
        }
        
        public String getMessage() {
            return message;
        }
        
        public String getStatus() {
            return status;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
    }
}
