package com.mdsadrulhasan.appy99lisence;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.mdsadrulhasan.appy99lisence.recharge.PermissionHelper;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeService;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomDialFunction;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomService;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DashboardActivity extends AppCompatActivity {

    private static final String TAG = "DashboardActivity";

    // Intent extra keys
    public static final String EXTRA_LICENSE_KEY = "license_key";
    public static final String EXTRA_DOMAIN_URL = "domain_url";
    public static final String EXTRA_ACTIVATION_STATUS = "activation_status";
    public static final String EXTRA_EXPIRATION_TIMESTAMP = "expiration_timestamp";
    public static final String EXTRA_DEVICE_ID = "device_id";
    public static final String EXTRA_DEVICE_INFO = "device_info";

    // UI Components
    private TextView welcomeText;
    private TextView licenseInfoText;
    private TextView domainInfoText;
    private TextView expirationInfoText;
    private TextView deviceInfoText;
    private Button backButton;

    // Telecom UI Components
    private LinearLayout telecomDashboardSection;
    private LinearLayout telecomNavHome;
    private LinearLayout telecomNavBanking;
    private LinearLayout telecomNavSettings;
    private LinearLayout telecomNavMonitor;
    private FrameLayout telecomContentContainer;

    // Recharge UI Components
    private LinearLayout rechargeSection;
    private CardView rechargeCard;
    private Button rechargeButton;
    private Button rechargeHistoryButton;
    private Button rechargeSettingsButton;
    private TextView rechargeStatusText;

    // Data from Intent
    private String licenseKey;
    private String domainUrl;
    private boolean activationStatus;
    private long expirationTimestamp;
    private String deviceId;
    private String deviceInfo;

    // Recharge functionality
    private SharedPreferences preferences;
    private boolean rechargeModuleEnabled = true;

    // Telecom functionality
    private TelecomDialFunction telecomDialFunction;
    private String currentTelecomTab = "home";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_dashboard);

        // Initialize preferences
        preferences = PreferenceManager.getDefaultSharedPreferences(this);

        // Initialize UI components
        initializeUI();

        // Get data from Intent
        extractIntentData();

        // Validate received data
        if (validateData()) {
            // Display dashboard information
            displayDashboardInfo();

            // Initialize recharge functionality
            initializeRechargeModule();

            // Initialize telecom functionality
            initializeTelecomModule();
        } else {
            // Handle invalid data
            handleInvalidData();
        }

        Log.d(TAG, "DashboardActivity created");
    }

    /**
     * Initialize UI components
     */
    private void initializeUI() {
        welcomeText = findViewById(R.id.welcomeText);
        licenseInfoText = findViewById(R.id.licenseInfoText);
        domainInfoText = findViewById(R.id.domainInfoText);
        expirationInfoText = findViewById(R.id.expirationInfoText);
        deviceInfoText = findViewById(R.id.deviceInfoText);
        backButton = findViewById(R.id.backButton);

        // Initialize telecom UI components
        telecomDashboardSection = findViewById(R.id.telecomDashboardSection);
        telecomNavHome = findViewById(R.id.telecom_nav_home);
        telecomNavBanking = findViewById(R.id.telecom_nav_banking);
        telecomNavSettings = findViewById(R.id.telecom_nav_settings);
        telecomNavMonitor = findViewById(R.id.telecom_nav_monitor);
        telecomContentContainer = findViewById(R.id.telecom_content_container);

        // Initialize recharge UI components
        rechargeSection = findViewById(R.id.rechargeSection);
        rechargeCard = findViewById(R.id.rechargeCard);
        rechargeButton = findViewById(R.id.rechargeButton);
        rechargeHistoryButton = findViewById(R.id.rechargeHistoryButton);
        rechargeSettingsButton = findViewById(R.id.rechargeSettingsButton);
        rechargeStatusText = findViewById(R.id.rechargeStatusText);

        // Set up back button click listener
        backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish(); // Close dashboard and return to MainActivity
            }
        });

        // Set up telecom navigation click listeners
        setupTelecomNavigationListeners();

        // Set up recharge button click listeners
        setupRechargeButtonListeners();
    }

    /**
     * Extract data from Intent extras
     */
    private void extractIntentData() {
        Intent intent = getIntent();

        licenseKey = intent.getStringExtra(EXTRA_LICENSE_KEY);
        domainUrl = intent.getStringExtra(EXTRA_DOMAIN_URL);
        activationStatus = intent.getBooleanExtra(EXTRA_ACTIVATION_STATUS, false);
        expirationTimestamp = intent.getLongExtra(EXTRA_EXPIRATION_TIMESTAMP, 0);
        deviceId = intent.getStringExtra(EXTRA_DEVICE_ID);
        deviceInfo = intent.getStringExtra(EXTRA_DEVICE_INFO);

        Log.d(TAG, "Received data - License: " + (licenseKey != null ? licenseKey.substring(0, Math.min(licenseKey.length(), 8)) + "..." : "null") +
                   ", Domain: " + domainUrl + ", Activated: " + activationStatus);
    }

    /**
     * Validate received data
     */
    private boolean validateData() {
        if (licenseKey == null || licenseKey.isEmpty()) {
            Log.e(TAG, "Invalid license key received");
            return false;
        }

        if (domainUrl == null || domainUrl.isEmpty()) {
            Log.e(TAG, "Invalid domain URL received");
            return false;
        }

        if (!activationStatus) {
            Log.e(TAG, "License not activated");
            return false;
        }

        if (expirationTimestamp <= 0) {
            Log.w(TAG, "Invalid expiration timestamp");
            // Don't fail validation for this, just log warning
        }

        return true;
    }

    /**
     * Display dashboard information
     */
    private void displayDashboardInfo() {
        // Welcome message
        welcomeText.setText("🎉 Welcome to Your Dashboard!");

        // License information
        String maskedLicense = maskLicenseKey(licenseKey);
        licenseInfoText.setText("📄 License: " + maskedLicense);

        // Domain information
        domainInfoText.setText("🌐 Domain: " + domainUrl);

        // Expiration information
        if (expirationTimestamp > 0) {
            String expirationDate = formatTimestamp(expirationTimestamp);
            long timeRemaining = expirationTimestamp - System.currentTimeMillis();

            if (timeRemaining > 0) {
                String timeRemainingStr = formatTimeRemaining(timeRemaining);
                expirationInfoText.setText("⏰ Expires: " + expirationDate + "\n" +
                                         "⏳ Time remaining: " + timeRemainingStr);
            } else {
                expirationInfoText.setText("⚠️ License has expired on: " + expirationDate);
                expirationInfoText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }
        } else {
            expirationInfoText.setText("⏰ Expiration: Not available");
        }

        // Device information
        if (deviceInfo != null && !deviceInfo.isEmpty()) {
            deviceInfoText.setText("📱 Device: " + deviceInfo + "\n" +
                                 "🔑 Device ID: " + (deviceId != null ? deviceId.substring(0, Math.min(deviceId.length(), 8)) + "..." : "N/A"));
        } else {
            deviceInfoText.setText("📱 Device information not available");
        }

        Log.d(TAG, "Dashboard information displayed successfully");
    }

    /**
     * Handle invalid data scenario
     */
    private void handleInvalidData() {
        Toast.makeText(this, "❌ Invalid authentication data. Returning to main screen.", Toast.LENGTH_LONG).show();

        // Show error message in UI
        welcomeText.setText("❌ Authentication Error");
        licenseInfoText.setText("Invalid license or authentication data received.");
        domainInfoText.setText("Please return to the main screen and try again.");
        expirationInfoText.setText("");
        deviceInfoText.setText("");

        Log.e(TAG, "Invalid data received, showing error state");

        // Auto-close after a delay
        welcomeText.postDelayed(new Runnable() {
            @Override
            public void run() {
                finish();
            }
        }, 3000); // Close after 3 seconds
    }

    /**
     * Mask license key for security (show only first and last few characters)
     */
    private String maskLicenseKey(String license) {
        if (license == null || license.length() <= 8) {
            return license;
        }

        int start = Math.min(4, license.length() / 3);
        int end = Math.max(license.length() - 4, license.length() * 2 / 3);

        StringBuilder masked = new StringBuilder();
        masked.append(license.substring(0, start));
        for (int i = start; i < end; i++) {
            masked.append("*");
        }
        masked.append(license.substring(end));

        return masked.toString();
    }

    /**
     * Format timestamp to readable date string with 12-hour format
     */
    private String formatTimestamp(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy 'at' hh:mm:ss a", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }

    /**
     * Format time remaining to readable string with seconds
     */
    private String formatTimeRemaining(long timeRemaining) {
        long days = timeRemaining / (24 * 60 * 60 * 1000);
        long hours = (timeRemaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
        long minutes = (timeRemaining % (60 * 60 * 1000)) / (60 * 1000);
        long seconds = (timeRemaining % (60 * 1000)) / 1000;

        if (days > 0) {
            return String.format(Locale.getDefault(), "%d days, %d hours, %d minutes, %d seconds", days, hours, minutes, seconds);
        } else if (hours > 0) {
            return String.format(Locale.getDefault(), "%d hours, %d minutes, %d seconds", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format(Locale.getDefault(), "%d minutes, %d seconds", minutes, seconds);
        } else {
            return String.format(Locale.getDefault(), "%d seconds", seconds);
        }
    }

    /**
     * Set up recharge button click listeners
     */
    private void setupRechargeButtonListeners() {
        if (rechargeButton != null) {
            rechargeButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openRechargeActivity();
                }
            });
        }

        if (rechargeHistoryButton != null) {
            rechargeHistoryButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openRechargeHistoryActivity();
                }
            });
        }

        if (rechargeSettingsButton != null) {
            rechargeSettingsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openRechargeSettingsActivity();
                }
            });
        }
    }

    /**
     * Initialize recharge module
     */
    private void initializeRechargeModule() {
        if (!rechargeModuleEnabled) {
            hideRechargeSection();
            return;
        }

        // Save license and device info for recharge module
        saveRechargeCredentials();

        // Show recharge section
        showRechargeSection();

        // Start recharge service if enabled
        if (preferences.getBoolean("auto_start_recharge_service", true)) {
            RechargeService.startRechargeService(this);
        }

        // Update recharge status
        updateRechargeStatus();

        Log.d(TAG, "Recharge module initialized");
    }

    /**
     * Save credentials for recharge module
     */
    private void saveRechargeCredentials() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString("license_key", licenseKey);
        editor.putString("device_id", deviceId);
        editor.putString("domain_url", domainUrl);
        editor.apply();
    }

    /**
     * Show recharge section
     */
    private void showRechargeSection() {
        if (rechargeSection != null) {
            rechargeSection.setVisibility(View.VISIBLE);
        }
        if (rechargeCard != null) {
            rechargeCard.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Hide recharge section
     */
    private void hideRechargeSection() {
        if (rechargeSection != null) {
            rechargeSection.setVisibility(View.GONE);
        }
        if (rechargeCard != null) {
            rechargeCard.setVisibility(View.GONE);
        }
    }

    /**
     * Update recharge status display
     */
    private void updateRechargeStatus() {
        if (rechargeStatusText != null) {
            boolean serviceRunning = preferences.getBoolean("recharge_service_running", false);
            boolean hasPermissions = PermissionHelper.hasAllRechargePermissions(this);

            String status;
            if (!hasPermissions) {
                status = "⚠️ Permissions required for recharge functionality";
            } else if (serviceRunning) {
                status = "🟢 Recharge service is running";
            } else {
                status = "🔴 Recharge service is stopped";
            }

            rechargeStatusText.setText(status);
        }
    }

    /**
     * Open recharge activity
     */
    private void openRechargeActivity() {
        try {
            Intent intent = new Intent(this, RechargeActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            intent.putExtra("domain_url", domainUrl);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge activity", e);
            Toast.makeText(this, "Error opening recharge module", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Open recharge history activity
     */
    private void openRechargeHistoryActivity() {
        try {
            Intent intent = new Intent(this, RechargeHistoryActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge history activity", e);
            Toast.makeText(this, "Error opening recharge history", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Open recharge settings activity
     */
    private void openRechargeSettingsActivity() {
        try {
            Intent intent = new Intent(this, RechargeSettingsActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge settings activity", e);
            Toast.makeText(this, "Error opening recharge settings", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Initialize telecom module
     */
    private void initializeTelecomModule() {
        try {
            // Initialize telecom dial function
            telecomDialFunction = new TelecomDialFunction(this);

            // Save telecom credentials
            saveTelecomCredentials();

            // Load default telecom tab
            loadTelecomTab("home");

            Log.d(TAG, "Telecom module initialized");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing telecom module", e);
        }
    }

    /**
     * Save credentials for telecom module
     */
    private void saveTelecomCredentials() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString("telecom_license_key", licenseKey);
        editor.putString("telecom_device_id", deviceId);
        editor.putString("telecom_domain_url", domainUrl);
        editor.apply();
    }

    /**
     * Setup telecom navigation listeners
     */
    private void setupTelecomNavigationListeners() {
        if (telecomNavHome != null) {
            telecomNavHome.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    selectTelecomTab("home");
                }
            });
        }

        if (telecomNavBanking != null) {
            telecomNavBanking.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    selectTelecomTab("banking");
                }
            });
        }

        if (telecomNavSettings != null) {
            telecomNavSettings.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    selectTelecomTab("settings");
                }
            });
        }

        if (telecomNavMonitor != null) {
            telecomNavMonitor.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    selectTelecomTab("monitor");
                }
            });
        }
    }

    /**
     * Select telecom tab
     */
    private void selectTelecomTab(String tabName) {
        // Reset all tab backgrounds
        if (telecomNavHome != null) telecomNavHome.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        if (telecomNavBanking != null) telecomNavBanking.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        if (telecomNavSettings != null) telecomNavSettings.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        if (telecomNavMonitor != null) telecomNavMonitor.setBackgroundColor(getResources().getColor(android.R.color.transparent));

        // Highlight selected tab
        switch (tabName) {
            case "home":
                if (telecomNavHome != null) telecomNavHome.setBackgroundColor(getResources().getColor(R.color.accent_blue));
                break;
            case "banking":
                if (telecomNavBanking != null) telecomNavBanking.setBackgroundColor(getResources().getColor(R.color.accent_blue));
                break;
            case "settings":
                if (telecomNavSettings != null) telecomNavSettings.setBackgroundColor(getResources().getColor(R.color.accent_blue));
                break;
            case "monitor":
                if (telecomNavMonitor != null) telecomNavMonitor.setBackgroundColor(getResources().getColor(R.color.accent_blue));
                break;
        }

        // Load tab content
        loadTelecomTab(tabName);
        currentTelecomTab = tabName;

        Log.d(TAG, "Selected telecom tab: " + tabName);
    }

    /**
     * Load telecom tab content
     */
    private void loadTelecomTab(String tabName) {
        if (telecomContentContainer == null) {
            return;
        }

        // Clear existing content
        telecomContentContainer.removeAllViews();

        // Inflate appropriate layout based on tab
        View tabContent = null;
        switch (tabName) {
            case "home":
                tabContent = getLayoutInflater().inflate(R.layout.fragment_telecom_home, telecomContentContainer, false);
                setupHomeTabContent(tabContent);
                break;
            case "banking":
                tabContent = getLayoutInflater().inflate(R.layout.fragment_telecom_banking, telecomContentContainer, false);
                setupBankingTabContent(tabContent);
                break;
            case "settings":
                tabContent = getLayoutInflater().inflate(R.layout.fragment_telecom_settings, telecomContentContainer, false);
                setupSettingsTabContent(tabContent);
                break;
            case "monitor":
                tabContent = getLayoutInflater().inflate(R.layout.fragment_telecom_monitor, telecomContentContainer, false);
                setupMonitorTabContent(tabContent);
                break;
        }

        if (tabContent != null) {
            telecomContentContainer.addView(tabContent);
        }
    }

    /**
     * Setup home tab content
     */
    private void setupHomeTabContent(View tabContent) {
        // Find views and setup home tab functionality
        TextView totalRechargesCount = tabContent.findViewById(R.id.total_recharges_count);
        TextView successRatePercentage = tabContent.findViewById(R.id.success_rate_percentage);

        if (totalRechargesCount != null) {
            totalRechargesCount.setText("0");
        }
        if (successRatePercentage != null) {
            successRatePercentage.setText("0%");
        }

        Log.d(TAG, "Home tab content setup completed");
    }

    /**
     * Setup banking tab content
     */
    private void setupBankingTabContent(View tabContent) {
        // Find views and setup banking tab functionality
        // This would include toggle buttons for bKash, Rocket, Nagad, etc.
        Log.d(TAG, "Banking tab content setup completed");
    }

    /**
     * Setup settings tab content
     */
    private void setupSettingsTabContent(View tabContent) {
        try {
            // Find views
            Button startServiceButton = tabContent.findViewById(R.id.start_telecom_service_button);
            Button stopServiceButton = tabContent.findViewById(R.id.stop_telecom_service_button);
            TextView serviceStatusText = tabContent.findViewById(R.id.telecom_service_status_text);

            // Setup service control buttons
            if (startServiceButton != null) {
                startServiceButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        startTelecomService();
                        if (serviceStatusText != null) {
                            serviceStatusText.setText("Service Status: Starting...");
                        }
                    }
                });
            }

            if (stopServiceButton != null) {
                stopServiceButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        stopTelecomService();
                        if (serviceStatusText != null) {
                            serviceStatusText.setText("Service Status: Stopped");
                        }
                    }
                });
            }

            // Update service status
            if (serviceStatusText != null) {
                boolean serviceRunning = getSharedPreferences("telecom_service", MODE_PRIVATE)
                        .getInt("service_running", 0) == 1;
                serviceStatusText.setText("Service Status: " + (serviceRunning ? "Running" : "Stopped"));
            }

            Log.d(TAG, "Settings tab content setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up settings tab content", e);
        }
    }

    /**
     * Start telecom service
     */
    private void startTelecomService() {
        try {
            TelecomService.startTelecomService(this);
            Toast.makeText(this, "Telecom service started", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "Telecom service started");
        } catch (Exception e) {
            Log.e(TAG, "Error starting telecom service", e);
            Toast.makeText(this, "Error starting telecom service", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Stop telecom service
     */
    private void stopTelecomService() {
        try {
            TelecomService.stopTelecomService(this);
            Toast.makeText(this, "Telecom service stopped", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "Telecom service stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping telecom service", e);
            Toast.makeText(this, "Error stopping telecom service", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Setup monitor tab content
     */
    private void setupMonitorTabContent(View tabContent) {
        // Find views and setup monitor tab functionality
        // This would include log display, refresh buttons, etc.
        Log.d(TAG, "Monitor tab content setup completed");
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Update recharge status when returning to dashboard
        if (rechargeModuleEnabled) {
            updateRechargeStatus();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Cleanup telecom resources
        if (telecomDialFunction != null) {
            telecomDialFunction.cleanup();
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        Log.d(TAG, "Back button pressed, returning to MainActivity");
    }
}
