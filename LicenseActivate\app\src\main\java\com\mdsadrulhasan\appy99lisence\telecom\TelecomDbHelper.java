package com.mdsadrulhasan.appy99lisence.telecom;

import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.DatabaseUtils;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.preference.PreferenceManager;
import android.util.Log;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

/**
 * Database helper for telecom recharge operations
 * Integrated from backup/app project into LicenseActivate
 */
public class TelecomDbHelper extends SQLiteOpenHelper {

    private static final String TAG = "TelecomDbHelper";

    // Database constants
    public static final String DATABASE_NAME = "telecom_recharge.db";
    public static final int DATABASE_VERSION = 1;

    // Table names
    public static final String TABLE_RECHARGE_LOG = "recharge_log";
    public static final String TABLE_SETTINGS = "telecom_settings";
    public static final String TABLE_SMS_LOG = "sms_log";

    // Recharge log table columns
    public static final String COLUMN_ID = "id";
    public static final String COLUMN_ORDER_ID = "orderid";
    public static final String COLUMN_NUMBER = "number";
    public static final String COLUMN_AMOUNT = "amount";
    public static final String COLUMN_PCODE = "pcode";
    public static final String COLUMN_USSD = "ussd";
    public static final String COLUMN_ONE = "one";
    public static final String COLUMN_TWO = "two";
    public static final String COLUMN_THREE = "three";
    public static final String COLUMN_FOUR = "four";
    public static final String COLUMN_FIVE = "five";
    public static final String COLUMN_LINE = "line";
    public static final String COLUMN_TRIGGER = "triger";
    public static final String COLUMN_SLOT = "slot";
    public static final String COLUMN_INTERNAL_ID = "_id";
    public static final String COLUMN_TIME = "time";
    public static final String COLUMN_STATUS = "status";
    public static final String COLUMN_OSTATUS = "ostatus";
    public static final String COLUMN_JOB = "job";
    public static final String COLUMN_POWERLOAD = "powerload";
    public static final String COLUMN_RESEND = "resend";
    public static final String COLUMN_API_RESPONSE = "apiresponse";

    // Settings table columns
    public static final String COLUMN_SETTING_ID = "id";
    public static final String COLUMN_TYPE = "type";
    public static final String COLUMN_SIM = "sim";
    public static final String COLUMN_POS = "pos";

    // SMS log table columns
    public static final String COLUMN_SMS_ID = "id";
    public static final String COLUMN_BODY = "body";
    public static final String COLUMN_SENDER = "sender";
    public static final String COLUMN_DELIVERY = "delivery";

    private Context appContext;

    public TelecomDbHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
        this.appContext = context;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        try {
            // Create recharge log table
            String createRechargeLogTable = "CREATE TABLE " + TABLE_RECHARGE_LOG + " (" +
                    COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COLUMN_API_RESPONSE + " TEXT, " +
                    COLUMN_ORDER_ID + " TEXT, " +
                    COLUMN_NUMBER + " TEXT, " +
                    COLUMN_AMOUNT + " TEXT, " +
                    COLUMN_PCODE + " TEXT, " +
                    COLUMN_USSD + " TEXT, " +
                    COLUMN_ONE + " TEXT, " +
                    COLUMN_TWO + " TEXT, " +
                    COLUMN_THREE + " TEXT, " +
                    COLUMN_FOUR + " TEXT, " +
                    COLUMN_FIVE + " TEXT, " +
                    COLUMN_LINE + " INTEGER, " +
                    COLUMN_TRIGGER + " INTEGER, " +
                    COLUMN_SLOT + " INTEGER, " +
                    COLUMN_INTERNAL_ID + " INTEGER, " +
                    COLUMN_TIME + " DATETIME DEFAULT CURRENT_TIMESTAMP, " +
                    COLUMN_STATUS + " TEXT DEFAULT '0', " +
                    COLUMN_OSTATUS + " TEXT DEFAULT '0', " +
                    COLUMN_JOB + " TEXT DEFAULT '1', " +
                    COLUMN_POWERLOAD + " INTEGER, " +
                    COLUMN_RESEND + " INTEGER DEFAULT 0" +
                    ")";

            // Create settings table
            String createSettingsTable = "CREATE TABLE " + TABLE_SETTINGS + " (" +
                    COLUMN_SETTING_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COLUMN_TYPE + " TEXT, " +
                    COLUMN_SIM + " TEXT, " +
                    COLUMN_POS + " INTEGER" +
                    ")";

            // Create SMS log table
            String createSmsLogTable = "CREATE TABLE " + TABLE_SMS_LOG + " (" +
                    COLUMN_SMS_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COLUMN_BODY + " TEXT, " +
                    COLUMN_SENDER + " TEXT, " +
                    COLUMN_DELIVERY + " TEXT" +
                    ")";

            db.execSQL(createRechargeLogTable);
            db.execSQL(createSettingsTable);
            db.execSQL(createSmsLogTable);

            Log.d(TAG, "Telecom database tables created successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error creating telecom database tables", e);
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "Upgrading database from version " + oldVersion + " to " + newVersion);

        // Drop existing tables and recreate
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_RECHARGE_LOG);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SETTINGS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SMS_LOG);
        onCreate(db);
    }

    /**
     * Insert SMS message
     */
    public boolean insertSMS(String body, String sender, String delivery) {
        try {
            SQLiteDatabase db = getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put(COLUMN_BODY, body);
            values.put(COLUMN_SENDER, sender);
            values.put(COLUMN_DELIVERY, delivery);

            long result = db.insert(TABLE_SMS_LOG, null, values);
            db.close();

            Log.d(TAG, "SMS inserted with ID: " + result);
            return result != -1;

        } catch (Exception e) {
            Log.e(TAG, "Error inserting SMS", e);
            return false;
        }
    }

    /**
     * Check if order already exists
     */
    public boolean orderExists(String orderId) {
        try {
            SQLiteDatabase db = getReadableDatabase();
            Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_RECHARGE_LOG + " WHERE " + COLUMN_ORDER_ID + " = ?", new String[]{orderId});
            boolean exists = cursor.getCount() > 0;
            cursor.close();
            db.close();
            return exists;
        } catch (Exception e) {
            Log.e(TAG, "Error checking if order exists", e);
            return false;
        }
    }

    /**
     * Insert recharge contact/order
     */
    public boolean insertContact(String orderId, String number, String amount, String ussd, String pcode,
                                int line, String one, String two, String three, String four, String five,
                                int trigger, int slot, int powerload, int status, int resend) {
        try {
            SQLiteDatabase db = getWritableDatabase();
            ContentValues values = new ContentValues();

            values.put(COLUMN_ORDER_ID, orderId);
            values.put(COLUMN_RESEND, resend);
            values.put(COLUMN_NUMBER, number);
            values.put(COLUMN_AMOUNT, amount);
            values.put(COLUMN_STATUS, status);

            if (ussd != null && !ussd.isEmpty()) {
                values.put(COLUMN_USSD, ussd);
            }
            if (pcode != null && !pcode.isEmpty()) {
                values.put(COLUMN_PCODE, pcode);
            }

            values.put(COLUMN_ONE, one);
            values.put(COLUMN_TWO, two);
            values.put(COLUMN_THREE, three);
            values.put(COLUMN_FOUR, four);
            values.put(COLUMN_FIVE, five);
            values.put(COLUMN_LINE, line);
            values.put(COLUMN_POWERLOAD, powerload);
            values.put(COLUMN_TRIGGER, trigger);
            values.put(COLUMN_SLOT, slot);

            if (!orderExists(orderId)) {
                long result = db.insert(TABLE_RECHARGE_LOG, null, values);

                // Send broadcast for new order
                Intent intent = new Intent("TelecomBackgroundService");
                intent.putExtra(COLUMN_ID, orderId);
                intent.putExtra("action", "insert");
                LocalBroadcastManager.getInstance(appContext).sendBroadcast(intent);

                Log.d(TAG, "New recharge order inserted: " + orderId);
                return result != -1;
            } else {
                // Update existing order
                int rowsAffected = db.update(TABLE_RECHARGE_LOG, values, COLUMN_ORDER_ID + " = ?", new String[]{orderId});

                // Send broadcast for updated order
                Intent intent = new Intent("TelecomBackgroundService");
                intent.putExtra(COLUMN_ID, orderId);
                intent.putExtra("action", "update");
                LocalBroadcastManager.getInstance(appContext).sendBroadcast(intent);

                Log.d(TAG, "Recharge order updated: " + orderId);
                return rowsAffected > 0;
            }

        } catch (Exception e) {
            Log.e(TAG, "Error inserting/updating contact", e);
            return false;
        } finally {
            try {
                SQLiteDatabase db = getWritableDatabase();
                if (db != null && db.isOpen()) {
                    db.close();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error closing database", e);
            }
        }
    }

    /**
     * Get all recharge data
     */
    public Cursor getAllData() {
        try {
            SQLiteDatabase db = getReadableDatabase();
            Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_RECHARGE_LOG, null);
            if (cursor != null) {
                cursor.moveToFirst();
            }
            return cursor;
        } catch (Exception e) {
            Log.e(TAG, "Error getting all data", e);
            return null;
        }
    }

    /**
     * Get data by order ID
     */
    public Cursor getData(String orderId) {
        try {
            SQLiteDatabase db = getReadableDatabase();
            Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_RECHARGE_LOG + " WHERE " + COLUMN_ORDER_ID + " = ?", new String[]{orderId});
            if (cursor != null) {
                cursor.moveToFirst();
            }
            return cursor;
        } catch (Exception e) {
            Log.e(TAG, "Error getting data for order: " + orderId, e);
            return null;
        }
    }

    /**
     * Get pending recharges
     */
    public Cursor getPendingRecharges() {
        try {
            SQLiteDatabase db = getReadableDatabase();
            Cursor cursor = db.query(TABLE_RECHARGE_LOG,
                    new String[]{COLUMN_ID, COLUMN_ORDER_ID, COLUMN_AMOUNT, COLUMN_USSD, COLUMN_SLOT},
                    COLUMN_STATUS + "=0", null, null, null, COLUMN_ID + " ASC");
            if (cursor != null) {
                cursor.moveToFirst();
            }
            return cursor;
        } catch (Exception e) {
            Log.e(TAG, "Error getting pending recharges", e);
            return null;
        }
    }

    /**
     * Update recharge status
     */
    public boolean updateRechargeStatus(String orderId, String status, String response) {
        try {
            SQLiteDatabase db = getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put(COLUMN_STATUS, status);
            if (response != null) {
                values.put(COLUMN_API_RESPONSE, response);
            }
            values.put(COLUMN_OSTATUS, "1"); // Mark as processed

            int rowsAffected = db.update(TABLE_RECHARGE_LOG, values, COLUMN_ORDER_ID + " = ?", new String[]{orderId});
            db.close();

            Log.d(TAG, "Updated recharge status for order: " + orderId + ", rows affected: " + rowsAffected);
            return rowsAffected > 0;
        } catch (Exception e) {
            Log.e(TAG, "Error updating recharge status", e);
            return false;
        }
    }

    /**
     * Get number of rows
     */
    public int getRowCount() {
        try {
            SQLiteDatabase db = getReadableDatabase();
            int count = (int) DatabaseUtils.queryNumEntries(db, TABLE_RECHARGE_LOG);
            db.close();
            return count;
        } catch (Exception e) {
            Log.e(TAG, "Error getting row count", e);
            return 0;
        }
    }

    /**
     * Save preference
     */
    public void savePreference(String key, String value) {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(appContext).edit();
        editor.putString(key, value);
        editor.apply();
    }

    /**
     * Get preference
     */
    public static String getPreference(String key, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(key, null);
    }

    /**
     * Get recent logs for display
     */
    public java.util.List<com.mdsadrulhasan.appy99lisence.telecom.TelecomLogEntry> getRecentLogs(int limit) {
        java.util.List<com.mdsadrulhasan.appy99lisence.telecom.TelecomLogEntry> logs = new java.util.ArrayList<>();
        try {
            SQLiteDatabase db = getReadableDatabase();
            Cursor cursor = db.query(TABLE_RECHARGE_LOG,
                    new String[]{COLUMN_ORDER_ID, COLUMN_NUMBER, COLUMN_AMOUNT, COLUMN_STATUS, COLUMN_TIME},
                    null, null, null, null, COLUMN_TIME + " DESC", String.valueOf(limit));

            if (cursor != null && cursor.moveToFirst()) {
                do {
                    String orderId = cursor.getString(cursor.getColumnIndex(COLUMN_ORDER_ID));
                    String number = cursor.getString(cursor.getColumnIndex(COLUMN_NUMBER));
                    String amount = cursor.getString(cursor.getColumnIndex(COLUMN_AMOUNT));
                    String status = cursor.getString(cursor.getColumnIndex(COLUMN_STATUS));
                    String time = cursor.getString(cursor.getColumnIndex(COLUMN_TIME));

                    String message = "Order: " + orderId + " | " + number + " | " + amount + " BDT";
                    String statusText = "0".equals(status) ? "Pending" : "1".equals(status) ? "Success" : "Failed";

                    logs.add(new com.mdsadrulhasan.appy99lisence.telecom.TelecomLogEntry(message, statusText, System.currentTimeMillis()));
                } while (cursor.moveToNext());
                cursor.close();
            }
            db.close();
        } catch (Exception e) {
            Log.e(TAG, "Error getting recent logs", e);
        }
        return logs;
    }

    /**
     * Get total log count
     */
    public int getTotalLogCount() {
        try {
            SQLiteDatabase db = getReadableDatabase();
            int count = (int) DatabaseUtils.queryNumEntries(db, TABLE_RECHARGE_LOG);
            db.close();
            return count;
        } catch (Exception e) {
            Log.e(TAG, "Error getting total log count", e);
            return 0;
        }
    }

    /**
     * Get last activity time
     */
    public String getLastActivityTime() {
        try {
            SQLiteDatabase db = getReadableDatabase();
            Cursor cursor = db.query(TABLE_RECHARGE_LOG,
                    new String[]{COLUMN_TIME},
                    null, null, null, null, COLUMN_TIME + " DESC", "1");

            String lastTime = null;
            if (cursor != null && cursor.moveToFirst()) {
                lastTime = cursor.getString(cursor.getColumnIndex(COLUMN_TIME));
                cursor.close();
            }
            db.close();
            return lastTime;
        } catch (Exception e) {
            Log.e(TAG, "Error getting last activity time", e);
            return null;
        }
    }

    /**
     * Clear all logs
     */
    public void clearAllLogs() {
        try {
            SQLiteDatabase db = getWritableDatabase();
            db.delete(TABLE_RECHARGE_LOG, null, null);
            db.delete(TABLE_SMS_LOG, null, null);
            db.close();
            Log.d(TAG, "All logs cleared");
        } catch (Exception e) {
            Log.e(TAG, "Error clearing all logs", e);
        }
    }
}
