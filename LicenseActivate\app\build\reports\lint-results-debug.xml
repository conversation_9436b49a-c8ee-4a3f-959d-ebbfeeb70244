<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.0">

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="                List&lt;SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();"
        errorLine2="                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="148"
            column="60"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="                List&lt;SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();"
        errorLine2="                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="237"
            column="60"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="                List&lt;SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();"
        errorLine2="                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="258"
            column="60"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="                List&lt;SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();"
        errorLine2="                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="276"
            column="60"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="            List&lt;PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java"
            line="88"
            column="54"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="                    List&lt;PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();"
        errorLine2="                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java"
            line="230"
            column="62"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="                    List&lt;PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();"
        errorLine2="                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java"
            line="253"
            column="62"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            switch (transaction.status.toLowerCase()) {"
        errorLine2="                                       ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="266"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            if (messageBody.toLowerCase().contains(&quot;failed&quot;) || "
        errorLine2="                            ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="220"
            column="29"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                messageBody.toLowerCase().contains(&quot;error&quot;) ||"
        errorLine2="                            ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="221"
            column="29"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                messageBody.toLowerCase().contains(&quot;insufficient&quot;)) {"
        errorLine2="                            ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="222"
            column="29"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String normalizedSender = sender.toLowerCase().trim();"
        errorLine2="                                         ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java"
            line="94"
            column="42"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String normalizedMessage = message.toLowerCase().trim();"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java"
            line="95"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String normalizedMessage = message.toLowerCase().trim();"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java"
            line="223"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        switch (entry.getStatus().toLowerCase()) {"
        errorLine2="                                  ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomLogAdapter.java"
            line="52"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String lowerMessage = message.toLowerCase();"
        errorLine2="                                      ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomSmsReceiver.java"
            line="83"
            column="39"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String lowerSender = sender.toLowerCase();"
        errorLine2="                                    ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomSmsReceiver.java"
            line="84"
            column="37"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String lowerMessage = message.toLowerCase();"
        errorLine2="                                      ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomSmsReceiver.java"
            line="242"
            column="39"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;ussd&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="100"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;balance&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="101"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;recharge&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="102"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;tk&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="103"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;taka&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="104"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;successful&quot;) ||"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="105"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    dialogText.toLowerCase().contains(&quot;failed&quot;))) {"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="106"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String lowerResponse = response.toLowerCase();"
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomUSSDService.java"
            line="272"
            column="41"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    transaction.orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));"
        errorLine2="                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="138"
            column="60"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    transaction.phoneNumber = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_PHONE_NUMBER));"
        errorLine2="                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="139"
            column="64"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    transaction.amount = cursor.getDouble(cursor.getColumnIndex(RechargeDbHelper.COLUMN_AMOUNT));"
        errorLine2="                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="140"
            column="59"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    transaction.operator = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_OPERATOR));"
        errorLine2="                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="141"
            column="61"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    transaction.status = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_STATUS));"
        errorLine2="                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="142"
            column="59"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    transaction.createdAt = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_CREATED_AT));"
        errorLine2="                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="143"
            column="62"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    transaction.smsResponse = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SMS_RESPONSE));"
        errorLine2="                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="144"
            column="64"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="145"
            column="51"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String ussdCode = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_USSD_CODE));"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="146"
            column="52"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                int simSlot = cursor.getInt(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SIM_SLOT));"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="147"
            column="45"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    long smsId = cursor.getLong(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ID));"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="185"
            column="49"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    String sender = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SENDER));"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="186"
            column="54"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    String messageBody = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_MESSAGE_BODY));"
        errorLine2="                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="187"
            column="59"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                    String orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));"
        errorLine2="                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeService.java"
            line="188"
            column="55"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String dbId = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_ID));"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java"
            line="283"
            column="48"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String orderId = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_ORDER_ID));"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java"
            line="284"
            column="51"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                String ussdCode = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_USSD));"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java"
            line="285"
            column="52"/>
    </issue>

    <issue
        id="Range"
        severity="Error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1"
        category="Correctness"
        priority="6"
        summary="Outside Range"
        explanation="Some parameters are required to be in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length."
        errorLine1="                int slot = cursor.getInt(cursor.getColumnIndex(TelecomDbHelper.COLUMN_SLOT));"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomService.java"
            line="286"
            column="42"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps (unless they also include `appop`). If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.BIND_ACCESSIBILITY_SERVICE&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="22"
            column="22"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps (unless they also include `appop`). If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.CALL_PRIVILEGED&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="41"
            column="22"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps (unless they also include `appop`). If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.MODIFY_PHONE_STATE&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="42"
            column="22"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.10.0 is available: 8.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.10.0&quot;"
        errorLine2="      ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="Typos"
        severity="Warning"
        message="&quot;Lisence&quot; is a common misspelling; did you mean &quot;License&quot; or &quot;Licence&quot;?"
        category="Correctness:Messages"
        priority="7"
        summary="Spelling error"
        explanation="This check looks through the string definitions, and if it finds any words that look like likely misspellings, they are flagged."
        errorLine1="    &lt;string name=&quot;app_name&quot;>Appy99Lisence&lt;/string>"
        errorLine2="                                  ^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="2"
            column="35"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.CALL_PHONE&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="17"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.RECEIVE_SMS&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="19"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_SMS&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="20"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.SEND_SMS&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="21"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.CALL_PHONE&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="32"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.RECEIVE_SMS&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="34"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_SMS&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="35"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.SEND_SMS&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="36"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.CALL_PRIVILEGED&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="41"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="Error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag"
        category="Correctness:Chrome OS"
        priority="3"
        summary="Permission Implies Unsupported Chrome OS Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported large screen hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding &lt;uses-feature> element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/topic/arc/manifest.html#implied-features"
        urls="https://developer.android.com/topic/arc/manifest.html#implied-features"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.MODIFY_PHONE_STATE&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="42"
            column="6"/>
    </issue>

    <issue
        id="HardwareIds"
        severity="Warning"
        message="Using `getString` to get device identifiers is not recommended"
        category="Security"
        priority="6"
        summary="Hardware Id Usage"
        explanation="Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use `AdvertisingIdClient$Info#getId` and for analytics, use `InstanceId#getId`."
        url="https://developer.android.com/training/articles/user-data-ids.html"
        urls="https://developer.android.com/training/articles/user-data-ids.html"
        errorLine1="        return Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="861"
            column="16"/>
    </issue>

    <issue
        id="UnprotectedSMSBroadcastReceiver"
        severity="Warning"
        message="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents"
        category="Security"
        priority="6"
        summary="Unprotected SMS `BroadcastReceiver`"
        explanation="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents."
        url="https://goo.gle/UnprotectedSMSBroadcastReceiver"
        urls="https://goo.gle/UnprotectedSMSBroadcastReceiver"
        errorLine1="        &lt;receiver"
        errorLine2="         ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="117"
            column="10"/>
    </issue>

    <issue
        id="UnprotectedSMSBroadcastReceiver"
        severity="Warning"
        message="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents"
        category="Security"
        priority="6"
        summary="Unprotected SMS `BroadcastReceiver`"
        explanation="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents."
        url="https://goo.gle/UnprotectedSMSBroadcastReceiver"
        urls="https://goo.gle/UnprotectedSMSBroadcastReceiver"
        errorLine1="        &lt;receiver"
        errorLine2="         ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml"
            line="134"
            column="10"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="                    historyAdapter.notifyDataSetChanged();"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="156"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeSmsReceiver.java"
            line="55"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="55"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="84"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="100"
            column="29"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="126"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 &amp;&amp; subscriptionManager != null) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="147"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 &amp;&amp; subscriptionManager != null) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="236"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 &amp;&amp; subscriptionManager != null) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="257"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 &amp;&amp; subscriptionManager != null) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\telecom\TelecomDialFunction.java"
            line="275"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java"
            line="47"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is never &lt; 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (android.os.Build.VERSION.SDK_INT &lt; android.os.Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java"
            line="78"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java"
            line="227"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\USSDDialer.java"
            line="250"
            column="17"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#f5f5f5&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#f5f5f5&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#f5f5f5&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#f5f5f5&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.activity_telecom_dashboard` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.menu.bottom_navigation_menu` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.black` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.white` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.waiting` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;waiting&quot;>#FFAA00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.done` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;done&quot;>#00CC00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorPrimary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorPrimary&quot;>#6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.failed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;failed&quot;>#FF0000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.primary_blue` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;primary_blue&quot;>#3F51B5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.primary_blue_dark` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;primary_blue_dark&quot;>#1A237E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.primary_blue_light` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;primary_blue_light&quot;>#3949AB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="15"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.background_light` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;background_light&quot;>#EEEEEE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="17"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.card_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;card_background&quot;>#FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.text_primary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;text_primary&quot;>#212121&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.text_hint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;text_hint&quot;>#9E9E9E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.domain_button_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\domain_button_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.edit_text_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\edit_text_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.menu.menu_main` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot; xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\menu_main.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.nav_item_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\nav_item_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.rounded_corner` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\rounded_corner.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.status_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\status_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.telecom_dashboard_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;telecom_dashboard_title&quot;>Telecom Recharge System&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.telecom_home_tab` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;telecom_home_tab&quot;>Home&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.telecom_banking_tab` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;telecom_banking_tab&quot;>Banking&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.telecom_settings_tab` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;telecom_settings_tab&quot;>Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.telecom_monitor_tab` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;telecom_monitor_tab&quot;>Monitor&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.service_status_running` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;service_status_running&quot;>Service Status: Running&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.service_status_stopped` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;service_status_stopped&quot;>Service Status: Stopped&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.service_status_starting` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;service_status_starting&quot;>Service Status: Starting...&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.service_status_stopping` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;service_status_stopping&quot;>Service Status: Stopping...&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.start_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;start_service&quot;>Start Service&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.stop_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;stop_service&quot;>Stop Service&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.refresh` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;refresh&quot;>Refresh&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.clear_logs` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;clear_logs&quot;>Clear Logs&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.export_logs` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;export_logs&quot;>Export&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.sms_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;sms_service&quot;>SMS Service&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.accessibility_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;accessibility_service&quot;>Accessibility&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.power_optimize` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;power_optimize&quot;>Power Optimize&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.auto_start_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;auto_start_service&quot;>Auto Start Service&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.network_settings` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;network_settings&quot;>Network Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.system_settings` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;system_settings&quot;>System Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.mobile_banking_services` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;mobile_banking_services&quot;>Mobile Banking Services&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.banking_configuration` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;banking_configuration&quot;>Banking Configuration&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.auto_banking` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;auto_banking&quot;>Auto Banking&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.banking_delay` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;banking_delay&quot;>Banking Delay (seconds)&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.banking_status` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;banking_status&quot;>Banking Status&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.test_banking_connection` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;test_banking_connection&quot;>Test Banking Connection&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.monitor_controls` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;monitor_controls&quot;>Monitor Controls&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.live_monitor` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;live_monitor&quot;>Live Monitor&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.auto_scroll` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;auto_scroll&quot;>Auto Scroll&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.no_logs_available` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;no_logs_available&quot;>No logs available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.no_recent_activity` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;no_recent_activity&quot;>No recent activity&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.total_recharges` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;total_recharges&quot;>Total Recharges&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.success_rate` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;success_rate&quot;>Success Rate&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.recent_activity` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;recent_activity&quot;>Recent Activity&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="51"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.telecom_service_started` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;telecom_service_started&quot;>Telecom service started&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.telecom_service_stopped` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;telecom_service_stopped&quot;>Telecom service stopped&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_starting_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_starting_service&quot;>Error starting telecom service&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_stopping_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_stopping_service&quot;>Error stopping telecom service&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.telecom_card_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\telecom_card_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.toggle_off` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\toggle_off.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.toggle_on` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\toggle_on.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.toggle_selector` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\toggle_selector.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        severity="Warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        category="Usability:Typography"
        priority="5"
        summary="Ellipsis string can be replaced with ellipsis character"
        explanation="You can replace the string &quot;...&quot; with a dedicated ellipsis character, ellipsis character (\u2026, &amp;#8230;). This can help make the text more readable."
        url="https://en.wikipedia.org/wiki/Ellipsis"
        urls="https://en.wikipedia.org/wiki/Ellipsis"
        errorLine1="    &lt;string name=&quot;service_status_starting&quot;>Service Status: Starting...&lt;/string>"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="15"
            column="44"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        severity="Warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        category="Usability:Typography"
        priority="5"
        summary="Ellipsis string can be replaced with ellipsis character"
        explanation="You can replace the string &quot;...&quot; with a dedicated ellipsis character, ellipsis character (\u2026, &amp;#8230;). This can help make the text more readable."
        url="https://en.wikipedia.org/wiki/Ellipsis"
        urls="https://en.wikipedia.org/wiki/Ellipsis"
        errorLine1="    &lt;string name=&quot;service_status_stopping&quot;>Service Status: Stopping...&lt;/string>"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml"
            line="16"
            column="44"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                            &lt;Button"
        errorLine2="                             ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="247"
            column="30"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                            &lt;Button"
        errorLine2="                             ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="260"
            column="30"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                            &lt;Button"
        errorLine2="                             ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="274"
            column="30"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                        &lt;Button"
        errorLine2="                         ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="242"
            column="26"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                        &lt;Button"
        errorLine2="                         ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="255"
            column="26"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="320"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="333"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                    &lt;Button"
        errorLine2="                     ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="239"
            column="22"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                    &lt;Button"
        errorLine2="                     ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="250"
            column="22"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="38"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="48"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="59"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                    &lt;Button"
        errorLine2="                     ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="51"
            column="22"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                    &lt;Button"
        errorLine2="                     ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="62"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="81"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="123"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="85"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="106"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="157"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="177"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="197"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="192"
            column="22"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.cardview:cardview:1.0.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle"
            line="37"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.recyclerview:recyclerview:1.4.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle"
            line="40"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.preference:preference:1.2.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle"
            line="41"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.retrofit2:retrofit:2.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle"
            line="42"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.retrofit2:converter-gson:2.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle"
            line="43"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.okhttp3:logging-interceptor:4.12.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build.gradle"
            line="44"
            column="20"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                            &lt;ImageView"
        errorLine2="                             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="336"
            column="30"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                            &lt;ImageView"
        errorLine2="                             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="369"
            column="30"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                            &lt;ImageView"
        errorLine2="                             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="401"
            column="30"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                            &lt;ImageView"
        errorLine2="                             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="433"
            column="30"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="37"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="70"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="102"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="134"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                &lt;ImageView"
        errorLine2="                 ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="172"
            column="18"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        welcomeText.setText(&quot;🎉 Welcome to Your Dashboard!&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="205"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        licenseInfoText.setText(&quot;📄 License: &quot; + maskedLicense);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="209"
            column="33"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        licenseInfoText.setText(&quot;📄 License: &quot; + maskedLicense);"
        errorLine2="                                ~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="209"
            column="33"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        domainInfoText.setText(&quot;🌐 Domain: &quot; + domainUrl);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="212"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        domainInfoText.setText(&quot;🌐 Domain: &quot; + domainUrl);"
        errorLine2="                               ~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="212"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                expirationInfoText.setText(&quot;⏰ Expires: &quot; + expirationDate + &quot;\n&quot; +"
        errorLine2="                                           ^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="221"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                expirationInfoText.setText(&quot;⏰ Expires: &quot; + expirationDate + &quot;\n&quot; +"
        errorLine2="                                           ~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="221"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                                         &quot;⏳ Time remaining: &quot; + timeRemainingStr);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="222"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                expirationInfoText.setText(&quot;⚠️ License has expired on: &quot; + expirationDate);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="224"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                expirationInfoText.setText(&quot;⚠️ License has expired on: &quot; + expirationDate);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="224"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            expirationInfoText.setText(&quot;⏰ Expiration: Not available&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="228"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            deviceInfoText.setText(&quot;📱 Device: &quot; + deviceInfo + &quot;\n&quot; +"
        errorLine2="                                   ^">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="233"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            deviceInfoText.setText(&quot;📱 Device: &quot; + deviceInfo + &quot;\n&quot; +"
        errorLine2="                                   ~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="233"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                                 &quot;🔑 Device ID: &quot; + (deviceId != null ? deviceId.substring(0, Math.min(deviceId.length(), 8)) + &quot;...&quot; : &quot;N/A&quot;));"
        errorLine2="                                 ~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="234"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            deviceInfoText.setText(&quot;📱 Device information not available&quot;);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="236"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        welcomeText.setText(&quot;❌ Authentication Error&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="249"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        licenseInfoText.setText(&quot;Invalid license or authentication data received.&quot;);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="250"
            column="33"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        domainInfoText.setText(&quot;Please return to the main screen and try again.&quot;);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="251"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                            serviceStatusText.setText(&quot;Service Status: Starting...&quot;);"
        errorLine2="                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="661"
            column="55"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                            serviceStatusText.setText(&quot;Service Status: Stopped&quot;);"
        errorLine2="                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="673"
            column="55"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                serviceStatusText.setText(&quot;Service Status: &quot; + (serviceRunning ? &quot;Running&quot; : &quot;Stopped&quot;));"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="683"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                serviceStatusText.setText(&quot;Service Status: &quot; + (serviceRunning ? &quot;Running&quot; : &quot;Stopped&quot;));"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\DashboardActivity.java"
            line="683"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.setText(&quot;App is activated and running&quot;);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="188"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.setText(&quot;Approval pending... Checking status...&quot;);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="195"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.setText(&quot;Please enter your license key and PIN to activate&quot;);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="198"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        statusText.setText(&quot;Authenticating...&quot;);"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="226"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.setText(&quot;App activated successfully!&quot;);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="396"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        statusText.setText(&quot;Authentication failed&quot;);"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="474"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                        statusText.setText(&quot;Approval check timeout. Please try again later.&quot;);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="520"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    statusText.setText(&quot;Error checking approval status&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="619"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    statusText.setText(&quot;Still pending approval... (&quot; + approvalCheckCount + &quot;/&quot; + MAX_APPROVAL_CHECKS + &quot;)&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="658"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    statusText.setText(&quot;Still pending approval... (&quot; + approvalCheckCount + &quot;/&quot; + MAX_APPROVAL_CHECKS + &quot;)&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="658"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        activateButton.setText(&quot;Activated&quot;);"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="836"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.setText(&quot;Resuming approval status check...&quot;);"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="919"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            domainInfoText.setText(&quot;Your domain: &quot; + domain);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="969"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            domainInfoText.setText(&quot;Your domain: &quot; + domain);"
        errorLine2="                                   ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="969"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                        domainLoginButton.setText(&quot;🔄 Opening...&quot;);"
        errorLine2="                                                  ~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1024"
            column="51"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                        domainLoginButton.setText(&quot;🎛️ Open Dashboard&quot;);"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1026"
            column="51"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                expirationText.setText(&quot;License Expired&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1558"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                countdownText.setText(&quot;Please renew your license&quot;);"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1562"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            expirationText.setText(&quot;License expires: &quot; + expirationDate);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1583"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            expirationText.setText(&quot;License expires: &quot; + expirationDate);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1583"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    activateButton.setText(&quot;Activate License&quot;);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1932"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    statusText.setText(&quot;License expired. Please enter a new license key to continue.&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\MainActivity.java"
            line="1937"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.orderIdText.setText(&quot;Order: &quot; + transaction.orderId);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="257"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.orderIdText.setText(&quot;Order: &quot; + transaction.orderId);"
        errorLine2="                                       ~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="257"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.phoneNumberText.setText(&quot;📞 &quot; + transaction.phoneNumber);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="258"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.amountText.setText(&quot;💰 &quot; + transaction.amount + &quot; BDT&quot;);"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="259"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.amountText.setText(&quot;💰 &quot; + transaction.amount + &quot; BDT&quot;);"
        errorLine2="                                                                   ~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="259"
            column="68"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.operatorText.setText(&quot;📡 &quot; + transaction.operator);"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="260"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.statusText.setText(&quot;Status: &quot; + transaction.status);"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="261"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.statusText.setText(&quot;Status: &quot; + transaction.status);"
        errorLine2="                                      ~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="261"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            holder.dateText.setText(&quot;📅 &quot; + transaction.createdAt);"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\java\com\mdsadrulhasan\appy99lisence\recharge\RechargeHistoryActivity.java"
            line="262"
            column="37"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🎉 Welcome to Your Dashboard!&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;🎉 Welcome to Your Dashboard!&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="37"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Your license and domain information&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Your license and domain information&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="47"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📄 License Information&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📄 License Information&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="71"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;License: Loading...&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="81"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🌐 Domain Access&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;🌐 Domain Access&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="107"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Domain: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Domain: Loading...&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="117"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;⏰ License Expiration&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;⏰ License Expiration&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="143"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Expiration: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Expiration: Loading...&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="153"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📱 Device Information&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📱 Device Information&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="179"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Device: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Device: Loading...&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="189"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📱 Mobile Recharge Module&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;📱 Mobile Recharge Module&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="225"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔴 Recharge service is stopped&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;🔴 Recharge service is stopped&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="235"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💳 Recharge&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;💳 Recharge&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="253"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📊 History&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;📊 History&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="267"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;⚙️ Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;⚙️ Settings&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="280"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;Home&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="346"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;Banking&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="379"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;Settings&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="411"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;Monitor&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="443"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔙 Back to Main&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;🔙 Back to Main&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="464"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🎛️ Dashboard - License Management System&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;🎛️ Dashboard - License Management System&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml"
            line="487"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Activation&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;License Activation&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="38"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter your license key and PIN to activate&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Enter your license key and PIN to activate&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="48"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Key&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;License Key&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="74"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter your license key&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter your license key&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="85"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;PIN&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;PIN&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="116"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter your PIN&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter your PIN&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="127"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Activate License&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Activate License&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="145"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Please enter your license key and PIN to activate&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Please enter your license key and PIN to activate&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="160"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🌐&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;🌐&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="202"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Domain Access&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;Domain Access&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="209"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Your domain: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;Your domain: Loading...&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="221"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🎛️ Open Dashboard&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;🎛️ Open Dashboard&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="231"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💡 View your license details and domain information&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;💡 View your license details and domain information&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="244"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📅&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;📅&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="288"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                                android:text=&quot;License Status&quot;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="295"
            column="33"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;License expires: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;License expires: Loading...&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="307"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Time remaining: Calculating...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;Time remaining: Calculating...&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="317"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💡 You will receive warnings 2 days before expiration&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;💡 You will receive warnings 2 days before expiration&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="337"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;👨‍💻&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;👨‍💻&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="374"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Md Sadrul Hasan Dider&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Md Sadrul Hasan Dider&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="381"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;© 2025 License Activation System&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;© 2025 License Activation System&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml"
            line="392"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📱 Mobile Recharge&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;📱 Mobile Recharge&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="35"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Submit mobile recharge requests&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Submit mobile recharge requests&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="45"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💳 Recharge Details&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;💳 Recharge Details&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="69"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📞 Phone Number&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📞 Phone Number&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="79"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter phone number&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter phone number&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="90"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💰 Amount (BDT)&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;💰 Amount (BDT)&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="100"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter amount&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter amount&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="111"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📡 Select Operator&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📡 Select Operator&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="121"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📱 Select SIM Slot&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📱 Select SIM Slot&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="139"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🚀 Submit Recharge&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;🚀 Submit Recharge&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="158"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📊 Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📊 Status&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="186"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Ready to submit recharge&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Ready to submit recharge&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="196"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;ℹ️ Instructions&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;ℹ️ Instructions&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="224"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;1. Enter the phone number to recharge\n2. Enter the recharge amount\n3. Select the mobile operator\n4. Choose the SIM slot to use\n5. Click Submit to process the recharge\n\nThe app will automatically dial the USSD code for you.&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;1. Enter the phone number to recharge\n2. Enter the recharge amount\n3. Select the mobile operator\n4. Choose the SIM slot to use\n5. Click Submit to process the recharge\n\nThe app will automatically dial the USSD code for you.&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="233"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔙 Back to Dashboard&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;🔙 Back to Dashboard&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="247"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📱 Mobile Recharge Module - License Management System&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;📱 Mobile Recharge Module - License Management System&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml"
            line="270"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📊 Recharge History&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;📊 Recharge History&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔄 Refresh&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;🔄 Refresh&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Loading recharge history...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Loading recharge history...&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="45"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📭&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;📭&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="76"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;No recharge history found&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;No recharge history found&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Your recharge transactions will appear here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Your recharge transactions will appear here&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="92"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔙 Back to Dashboard&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;🔙 Back to Dashboard&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml"
            line="111"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;⚙️ Recharge Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;⚙️ Recharge Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="35"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Configure your recharge preferences&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Configure your recharge preferences&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="45"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📱 SIM Configuration&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📱 SIM Configuration&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="69"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📡 SIM 1 Operator&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📡 SIM 1 Operator&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="79"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📡 SIM 2 Operator&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📡 SIM 2 Operator&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="97"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔄 Enable automatic recharge processing&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;🔄 Enable automatic recharge processing&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="116"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🌐 Server Configuration&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;🌐 Server Configuration&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="141"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔗 Server URL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;🔗 Server URL&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="151"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter server URL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter server URL&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="162"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔑 API PIN&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;🔑 API PIN&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="171"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter API PIN&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter API PIN&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="182"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;⏱️ Check Interval (seconds)&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;⏱️ Check Interval (seconds)&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="191"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Check interval in seconds&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Check interval in seconds&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="202"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;5&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;5&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="204"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🎛️ Service Control&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;🎛️ Service Control&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="229"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;▶️ Start Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;▶️ Start Service&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="248"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;⏹️ Stop Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                            android:text=&quot;⏹️ Stop Service&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="261"
            column="29"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📊 Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;📊 Status&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="291"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Loading settings...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Loading settings...&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="301"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💾 Save Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;💾 Save Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="326"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🔙 Back&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;🔙 Back&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="339"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;⚙️ Recharge Settings - License Management System&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;⚙️ Recharge Settings - License Management System&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml"
            line="364"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Home&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="47"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Banking&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="80"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Settings&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="112"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Monitor&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="144"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Automatic Mobile Recharge System&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Automatic Mobile Recharge System&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="185"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Device ID: Loading...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Device ID: Loading...&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="199"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Service Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Service Status&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="219"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Service Status: Checking...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Service Status: Checking...&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="229"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Start Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="246"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Stop Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Stop Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="257"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Network Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Network Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="277"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SIM A:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SIM A:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="295"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SIM B:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SIM B:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="321"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;System Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;System Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="350"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SMS Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SMS Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="368"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Accessibility&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Accessibility&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="398"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Power Optimize&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Power Optimize&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_telecom_dashboard.xml"
            line="427"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Home&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;M Banking&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Settings&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Monitor&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\bottom_navigation_menu.xml"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Mobile Banking Services&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Mobile Banking Services&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="30"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;bKash&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;bKash&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="49"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Rocket&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Rocket&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="78"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Nagad&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Nagad&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="106"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Banking Configuration&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Banking Configuration&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="142"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Auto Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Auto Banking&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="161"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Banking Delay (seconds)&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Banking Delay (seconds)&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="187"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter delay in seconds&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;Enter delay in seconds&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="197"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;5&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;5&quot; />"
        errorLine2="                        ~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="200"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Banking Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Banking Status&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="222"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Banking services are ready&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Banking services are ready&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="232"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Banking Connection&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Test Banking Connection&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_banking.xml"
            line="241"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Total Recharges&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Total Recharges&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml"
            line="36"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;0&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml"
            line="45"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Success Rate&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Success Rate&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml"
            line="72"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;0%&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml"
            line="81"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Recent Activity&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Recent Activity&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml"
            line="107"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;No recent activity&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;No recent activity&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_home.xml"
            line="122"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Monitor Controls&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Monitor Controls&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="26"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Refresh&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Refresh&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="44"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Clear Logs&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Clear Logs&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="55"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Export&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Export&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="65"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Live Monitor&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Live Monitor&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="97"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;🟢 Active&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;🟢 Active&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="106"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Auto Scroll&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Auto Scroll&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="140"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;No logs available&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;No logs available&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_monitor.xml"
            line="161"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Service Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Service Status&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="30"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Service Status: Checking...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Service Status: Checking...&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="40"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Start Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="58"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Stop Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Stop Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="69"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Network Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Network Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="94"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SIM A:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SIM A:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="113"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SIM B:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SIM B:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="137"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;System Settings&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;System Settings&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="168"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SMS Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;SMS Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="188"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Accessibility&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Accessibility&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="217"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Power Optimize&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Power Optimize&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="246"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Auto Start Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Auto Start Service&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\fragment_telecom_settings.xml"
            line="274"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Order: RCH_123456&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Order: RCH_123456&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml"
            line="28"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Completed&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Completed&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml"
            line="37"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📞 +8801712345678&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;📞 +8801712345678&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml"
            line="62"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;💰 50 BDT&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;💰 50 BDT&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml"
            line="70"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📡 Grameenphone&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;📡 Grameenphone&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml"
            line="88"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;📅 2024-01-15 10:30&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;📅 2024-01-15 10:30&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml"
            line="96"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SMS: Recharge successful&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;SMS: Recharge successful&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml"
            line="107"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;12:34:56&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;12:34:56&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_telecom_log.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Log message&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Log message&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_telecom_log.xml"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Status&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Status&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_telecom_log.xml"
            line="34"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Mobile Banking&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Mobile Banking&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\menu_main.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Forward&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:title=&quot;Forward&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\menu\menu_main.xml"
            line="11"
            column="9"/>
    </issue>

</issues>
