<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="TelecomBackgroundService (used to send a broadcast)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDbHelper.java"
                            line="220"
                            column="33"
                            startOffset="9022"
                            endLine="220"
                            endColumn="71"
                            endOffset="9060"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDbHelper.java"
                            line="232"
                            column="33"
                            startOffset="9664"
                            endLine="232"
                            endColumn="71"
                            endOffset="9702"/>
                    </map>
                    <map id="android.intent.action.CALL (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
                            line="98"
                            column="33"
                            startOffset="3778"
                            endLine="98"
                            endColumn="63"
                            endOffset="3808"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
                            line="132"
                            column="33"
                            startOffset="5179"
                            endLine="132"
                            endColumn="63"
                            endOffset="5209"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
                            line="95"
                            column="45"
                            startOffset="3335"
                            endLine="95"
                            endColumn="75"
                            endOffset="3365"/>
                        <location id="3"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
                            line="121"
                            column="29"
                            startOffset="4272"
                            endLine="121"
                            endColumn="59"
                            endOffset="4302"/>
                    </map>
                    <map id="android.settings.APPLICATION_DETAILS_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
                            line="1282"
                            column="13"
                            startOffset="46412"
                            endLine="1282"
                            endColumn="75"
                            endOffset="46474"/>
                    </map>
                    <map id="android.settings.APP_NOTIFICATION_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
                            line="1279"
                            column="13"
                            startOffset="46247"
                            endLine="1279"
                            endColumn="72"
                            endOffset="46306"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.mdsadrulhasan.appy99lisence.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSmsReceiver"
                    boolean="true"/>
                <entry
                    name="com.mdsadrulhasan.appy99lisence.telecom.TelecomSmsReceiver"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.background_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="602"
            endLine="17"
            endColumn="35"
            endOffset="625"/>
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="24"
            endOffset="74"/>
        <location id="R.color.card_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="18"
            column="12"
            startOffset="653"
            endLine="18"
            endColumn="34"
            endOffset="675"/>
        <location id="R.color.colorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="281"
            endLine="9"
            endColumn="31"
            endOffset="300"/>
        <location id="R.color.done"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="242"
            endLine="8"
            endColumn="23"
            endOffset="253"/>
        <location id="R.color.failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="10"
            column="12"
            startOffset="328"
            endLine="10"
            endColumn="25"
            endOffset="341"/>
        <location id="R.color.primary_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="404"
            endLine="13"
            endColumn="31"
            endOffset="423"/>
        <location id="R.color.primary_blue_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="451"
            endLine="14"
            endColumn="36"
            endOffset="475"/>
        <location id="R.color.primary_blue_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="503"
            endLine="15"
            endColumn="37"
            endOffset="528"/>
        <location id="R.color.text_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="799"
            endLine="21"
            endColumn="28"
            endOffset="815"/>
        <location id="R.color.text_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="703"
            endLine="19"
            endColumn="31"
            endOffset="722"/>
        <location id="R.color.waiting"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="200"
            endLine="7"
            endColumn="26"
            endOffset="214"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="104"
            endLine="4"
            endColumn="24"
            endOffset="116"/>
        <location id="R.drawable.domain_button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/domain_button_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="28"
            endColumn="12"
            endOffset="799"/>
        <location id="R.drawable.edit_text_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="9"
            endOffset="314"/>
        <location id="R.drawable.nav_item_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/nav_item_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="16"
            endColumn="12"
            endOffset="510"/>
        <location id="R.drawable.rounded_corner"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="266"/>
        <location id="R.drawable.status_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/status_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="9"
            endOffset="314"/>
        <location id="R.drawable.telecom_card_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/telecom_card_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="9"
            endOffset="249"/>
        <location id="R.drawable.toggle_off"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_off.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="7"
            endColumn="9"
            endOffset="276"/>
        <location id="R.drawable.toggle_on"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_on.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="7"
            endColumn="9"
            endOffset="276"/>
        <location id="R.drawable.toggle_selector"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_selector.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="12"
            endOffset="314"/>
        <location id="R.layout.activity_telecom_dashboard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="446"
            endColumn="16"
            endOffset="19232"/>
        <location id="R.menu.bottom_navigation_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="8"
            endOffset="644"/>
        <location id="R.menu.menu_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="8"
            endOffset="467"/>
        <location id="R.string.accessibility_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1346"
            endLine="27"
            endColumn="41"
            endOffset="1374"/>
        <location id="R.string.auto_banking"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="1834"
            endLine="36"
            endColumn="32"
            endOffset="1853"/>
        <location id="R.string.auto_scroll"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2234"
            endLine="44"
            endColumn="31"
            endOffset="2252"/>
        <location id="R.string.auto_start_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1468"
            endLine="29"
            endColumn="38"
            endOffset="1493"/>
        <location id="R.string.banking_configuration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="1762"
            endLine="35"
            endColumn="41"
            endOffset="1790"/>
        <location id="R.string.banking_delay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="1888"
            endLine="37"
            endColumn="33"
            endOffset="1908"/>
        <location id="R.string.banking_status"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="1954"
            endLine="38"
            endColumn="34"
            endOffset="1975"/>
        <location id="R.string.clear_logs"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1166"
            endLine="22"
            endColumn="30"
            endOffset="1183"/>
        <location id="R.string.error_starting_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="2804"
            endLine="56"
            endColumn="42"
            endOffset="2833"/>
        <location id="R.string.error_stopping_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="2886"
            endLine="57"
            endColumn="42"
            endOffset="2915"/>
        <location id="R.string.export_logs"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1216"
            endLine="23"
            endColumn="31"
            endOffset="1234"/>
        <location id="R.string.live_monitor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="2180"
            endLine="43"
            endColumn="32"
            endOffset="2199"/>
        <location id="R.string.mobile_banking_services"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="34"
            column="13"
            startOffset="1686"
            endLine="34"
            endColumn="43"
            endOffset="1716"/>
        <location id="R.string.monitor_controls"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="2118"
            endLine="42"
            endColumn="36"
            endOffset="2141"/>
        <location id="R.string.network_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1534"
            endLine="30"
            endColumn="36"
            endOffset="1557"/>
        <location id="R.string.no_logs_available"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2286"
            endLine="45"
            endColumn="37"
            endOffset="2310"/>
        <location id="R.string.no_recent_activity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="2350"
            endLine="46"
            endColumn="38"
            endOffset="2375"/>
        <location id="R.string.power_optimize"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1410"
            endLine="28"
            endColumn="34"
            endOffset="1431"/>
        <location id="R.string.recent_activity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="2563"
            endLine="51"
            endColumn="35"
            endOffset="2585"/>
        <location id="R.string.refresh"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="1122"
            endLine="21"
            endColumn="27"
            endOffset="1136"/>
        <location id="R.string.service_status_running"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="673"
            endLine="13"
            endColumn="42"
            endOffset="702"/>
        <location id="R.string.service_status_starting"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="823"
            endLine="15"
            endColumn="43"
            endOffset="853"/>
        <location id="R.string.service_status_stopped"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="748"
            endLine="14"
            endColumn="42"
            endOffset="777"/>
        <location id="R.string.service_status_stopping"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="903"
            endLine="16"
            endColumn="43"
            endOffset="933"/>
        <location id="R.string.sms_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1294"
            endLine="26"
            endColumn="31"
            endOffset="1312"/>
        <location id="R.string.start_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="1012"
            endLine="19"
            endColumn="33"
            endOffset="1032"/>
        <location id="R.string.stop_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="1068"
            endLine="20"
            endColumn="32"
            endOffset="1087"/>
        <location id="R.string.success_rate"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="2509"
            endLine="50"
            endColumn="32"
            endOffset="2528"/>
        <location id="R.string.system_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1596"
            endLine="31"
            endColumn="35"
            endOffset="1618"/>
        <location id="R.string.telecom_banking_tab"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="466"
            endLine="8"
            endColumn="39"
            endOffset="492"/>
        <location id="R.string.telecom_dashboard_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="340"
            endLine="6"
            endColumn="43"
            endOffset="370"/>
        <location id="R.string.telecom_home_tab"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="416"
            endLine="7"
            endColumn="36"
            endOffset="439"/>
        <location id="R.string.telecom_monitor_tab"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="580"
            endLine="10"
            endColumn="39"
            endOffset="606"/>
        <location id="R.string.telecom_service_started"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="2652"
            endLine="54"
            endColumn="43"
            endOffset="2682"/>
        <location id="R.string.telecom_service_stopped"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2728"
            endLine="55"
            endColumn="43"
            endOffset="2758"/>
        <location id="R.string.telecom_settings_tab"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="522"
            endLine="9"
            endColumn="40"
            endOffset="549"/>
        <location id="R.string.test_banking_connection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="2012"
            endLine="39"
            endColumn="43"
            endOffset="2042"/>
        <location id="R.string.total_recharges"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="2449"
            endLine="49"
            endColumn="35"
            endOffset="2471"/>
        <entry
            name="model"
            string="attr[selectableItemBackground(R)],color[black(D),white(D),waiting(D),done(D),colorPrimary(D),failed(D),primary_blue(D),primary_blue_dark(D),primary_blue_light(D),accent_blue(U),background_light(D),card_background(D),text_primary(D),text_secondary(U),text_hint(D),success_green(U),warning_orange(U),error_red(U)],drawable[button_background(U),card_background(U),domain_button_background(D),edit_text_background(D),footer_background(U),gradient_background(U),ic_baseline_add_to_queue_24(U),ic_home_black_24dp(U),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_settings_black_24dp(U),ic_settings_cell_black_24dp(U),input_background(U),modern_button_background(U),modern_edit_text_background(U),modern_status_background(U),modern_toggle_off(U),modern_toggle_on(U),modern_toggle_selector(U),nav_item_background(D),rounded_corner(D),rounded_cornerss(U),spinner_background(U),status_background(D),telecom_card_background(D),toggle_off(D),toggle_on(D),toggle_selector(D)],id[welcomeText(U),licenseInfoText(U),domainInfoText(U),expirationInfoText(U),deviceInfoText(U),rechargeSection(U),rechargeCard(U),rechargeStatusText(U),rechargeButton(U),rechargeHistoryButton(U),rechargeSettingsButton(U),telecomDashboardSection(U),telecom_navigation_container(D),telecom_nav_home(U),telecom_nav_banking(U),telecom_nav_settings(U),telecom_nav_monitor(U),telecom_content_container(U),backButton(U),main(D),licenseKeyField(U),pinField(U),activateButton(U),statusText(U),domainLoginSection(U),domainLoginButton(U),expirationSection(U),expirationText(U),countdownText(U),expirationProgressBar(U),phoneNumberField(U),amountField(U),operatorSpinner(U),simSlotSpinner(U),submitRechargeButton(U),refreshButton(U),historyRecyclerView(U),emptyStateLayout(D),sim1OperatorSpinner(U),sim2OperatorSpinner(U),autoRechargeCheckbox(U),serverUrlField(U),apiPinField(U),checkIntervalField(U),startServiceButton(U),stopServiceButton(U),saveSettingsButton(U),telecom_dashboard(D),navigation_container(D),nav_home(D),nav_banking(D),nav_settings(D),nav_monitor(D),app_logo(D),device_id_display(D),service_status_text(D),start_service_button(D),stop_service_button(D),sim_a_spinner(D),sim_b_spinner(D),sms_service_setting(D),sms_service_toggle(D),accessibility_setting(D),accessibility_toggle(D),power_optimize_setting(D),power_optimize_toggle(D),bkash_toggle(D),rocket_toggle(D),nagad_toggle(D),auto_banking_toggle(D),banking_delay_input(D),banking_status_text(D),test_banking_button(D),total_recharges_count(U),success_rate_percentage(U),recent_activity_recycler(D),no_activity_text(D),refresh_monitor_button(D),clear_logs_button(D),export_logs_button(D),monitor_status_indicator(D),filter_spinner(D),auto_scroll_toggle(D),monitor_log_recycler(D),no_logs_text(D),telecom_service_status_text(U),start_telecom_service_button(U),stop_telecom_service_button(U),telecom_sim_a_spinner(D),telecom_sim_b_spinner(D),telecom_sms_service_toggle(D),telecom_accessibility_toggle(D),telecom_power_optimize_toggle(D),telecom_auto_start_toggle(D),orderIdText(U),phoneNumberText(U),amountText(U),operatorText(U),dateText(U),smsResponseText(D),log_time_text(U),log_message_text(U),log_status_text(U),home(D),bank(D),sms(D),mon(D),inter(D),forward(D)],layout[activity_dashboard(U),activity_main(U),activity_recharge(U),activity_recharge_history(U),activity_recharge_settings(U),activity_telecom_dashboard(D),fragment_telecom_banking(U),fragment_telecom_home(U),fragment_telecom_monitor(U),fragment_telecom_settings(U),item_recharge_history(U),item_telecom_log(U)],menu[bottom_navigation_menu(D),menu_main(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),telecom_accessibility_service_description(U),telecom_dashboard_title(D),telecom_home_tab(D),telecom_banking_tab(D),telecom_settings_tab(D),telecom_monitor_tab(D),service_status_running(D),service_status_stopped(D),service_status_starting(D),service_status_stopping(D),start_service(D),stop_service(D),refresh(D),clear_logs(D),export_logs(D),sms_service(D),accessibility_service(D),power_optimize(D),auto_start_service(D),network_settings(D),system_settings(D),mobile_banking_services(D),banking_configuration(D),auto_banking(D),banking_delay(D),banking_status(D),test_banking_connection(D),monitor_controls(D),live_monitor(D),auto_scroll(D),no_logs_available(D),no_recent_activity(D),total_recharges(D),success_rate(D),recent_activity(D),telecom_service_started(D),telecom_service_stopped(D),error_starting_service(D),error_stopping_service(D)],style[Theme_Appy99Lisence(U),Base_Theme_Appy99Lisence(U),Theme_Material3_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U),telecom_accessibility_service_config(U)];1c^1d,26^25^24,2f^2e^2d,9d^13^0^3e^1a^3f^3d^1f^40^1e^19,9e^18^22^21^23^17,9f^2a^13,a0^13,a1^2a^13,a2^18^0^62^1a^63^61^1f^64^1e^19^ab^29^28^13^26,a3^14^29^26^20^13,a4^14,a5^14^13^20^26,a6^14^13^29^26,a8^29,a9^1a^1f^1e^19,ab^1b^1c,ac^1b^1c,d5^d6,d6^d7,db^ae;;;"/>
    </map>

</incidents>
