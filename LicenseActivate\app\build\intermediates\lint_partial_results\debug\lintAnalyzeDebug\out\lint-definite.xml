<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="266"
            column="40"
            startOffset="9712"
            endLine="266"
            endColumn="51"
            endOffset="9723"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="220"
            column="29"
            startOffset="7714"
            endLine="220"
            endColumn="40"
            endOffset="7725"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="221"
            column="29"
            startOffset="7779"
            endLine="221"
            endColumn="40"
            endOffset="7790"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="222"
            column="29"
            startOffset="7842"
            endLine="222"
            endColumn="40"
            endOffset="7853"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeSmsReceiver.java"
            line="94"
            column="42"
            startOffset="3423"
            endLine="94"
            endColumn="53"
            endOffset="3434"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeSmsReceiver.java"
            line="95"
            column="44"
            startOffset="3488"
            endLine="95"
            endColumn="55"
            endOffset="3499"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeSmsReceiver.java"
            line="223"
            column="44"
            startOffset="8406"
            endLine="223"
            endColumn="55"
            endOffset="8417"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomLogAdapter.java"
            line="52"
            column="35"
            startOffset="1704"
            endLine="52"
            endColumn="46"
            endOffset="1715"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomSmsReceiver.java"
            line="83"
            column="39"
            startOffset="2819"
            endLine="83"
            endColumn="50"
            endOffset="2830"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomSmsReceiver.java"
            line="84"
            column="37"
            startOffset="2870"
            endLine="84"
            endColumn="48"
            endOffset="2881"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomSmsReceiver.java"
            line="242"
            column="39"
            startOffset="9075"
            endLine="242"
            endColumn="50"
            endOffset="9086"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="100"
            column="32"
            startOffset="3272"
            endLine="100"
            endColumn="43"
            endOffset="3283"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="101"
            column="32"
            startOffset="3337"
            endLine="101"
            endColumn="43"
            endOffset="3348"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="102"
            column="32"
            startOffset="3405"
            endLine="102"
            endColumn="43"
            endOffset="3416"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="103"
            column="32"
            startOffset="3474"
            endLine="103"
            endColumn="43"
            endOffset="3485"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="104"
            column="32"
            startOffset="3537"
            endLine="104"
            endColumn="43"
            endOffset="3548"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="105"
            column="32"
            startOffset="3602"
            endLine="105"
            endColumn="43"
            endOffset="3613"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="106"
            column="32"
            startOffset="3673"
            endLine="106"
            endColumn="43"
            endOffset="3684"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomUSSDService.java"
            line="272"
            column="41"
            startOffset="9098"
            endLine="272"
            endColumn="52"
            endOffset="9109"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="138"
            column="60"
            startOffset="4431"
            endLine="138"
            endColumn="115"
            endOffset="4486"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="139"
            column="64"
            startOffset="4552"
            endLine="139"
            endColumn="123"
            endOffset="4611"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="140"
            column="59"
            startOffset="4672"
            endLine="140"
            endColumn="112"
            endOffset="4725"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="141"
            column="61"
            startOffset="4788"
            endLine="141"
            endColumn="116"
            endOffset="4843"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="142"
            column="59"
            startOffset="4904"
            endLine="142"
            endColumn="112"
            endOffset="4957"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="143"
            column="62"
            startOffset="5021"
            endLine="143"
            endColumn="119"
            endOffset="5078"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="144"
            column="64"
            startOffset="5144"
            endLine="144"
            endColumn="123"
            endOffset="5203"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="145"
            column="51"
            startOffset="4446"
            endLine="145"
            endColumn="106"
            endOffset="4501"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="146"
            column="52"
            startOffset="4555"
            endLine="146"
            endColumn="108"
            endOffset="4611"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="147"
            column="45"
            startOffset="4658"
            endLine="147"
            endColumn="100"
            endOffset="4713"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="185"
            column="49"
            startOffset="6067"
            endLine="185"
            endColumn="98"
            endOffset="6116"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="186"
            column="54"
            startOffset="6172"
            endLine="186"
            endColumn="107"
            endOffset="6225"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="187"
            column="59"
            startOffset="6286"
            endLine="187"
            endColumn="118"
            endOffset="6345"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java"
            line="188"
            column="55"
            startOffset="6402"
            endLine="188"
            endColumn="110"
            endOffset="6457"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomService.java"
            line="283"
            column="48"
            startOffset="9337"
            endLine="283"
            endColumn="96"
            endOffset="9385"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomService.java"
            line="284"
            column="51"
            startOffset="9438"
            endLine="284"
            endColumn="105"
            endOffset="9492"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomService.java"
            line="285"
            column="52"
            startOffset="9546"
            endLine="285"
            endColumn="102"
            endOffset="9596"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomService.java"
            line="286"
            column="42"
            startOffset="9640"
            endLine="286"
            endColumn="92"
            endOffset="9690"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="22"
            column="22"
            startOffset="1150"
            endLine="22"
            endColumn="82"
            endOffset="1210"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="41"
            column="22"
            startOffset="2357"
            endLine="41"
            endColumn="71"
            endOffset="2406"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="42"
            column="22"
            startOffset="2432"
            endLine="42"
            endColumn="74"
            endOffset="2484"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.10.0 is available: 8.10.1">
        <fix-replace
            description="Change to 8.10.1"
            family="Update versions"
            robot="true"
            independent="true"
            oldString="8.10.0"
            replacement="8.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
    </incident>

    <incident
        id="Typos"
        severity="warning"
        message="&quot;Lisence&quot; is a common misspelling; did you mean &quot;License&quot; or &quot;Licence&quot;?">
        <fix-alternatives>
            <fix-replace
                description="Replace with &quot;License&quot;"
                oldString="Lisence"
                replacement="License"
                priority="0"/>
            <fix-replace
                description="Replace with &quot;Licence&quot;"
                oldString="Lisence"
                replacement="Licence"
                priority="0"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="2"
            column="35"
            startOffset="46"
            endLine="2"
            endColumn="35"
            endOffset="53"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="861"
            column="16"
            startOffset="30614"
            endLine="861"
            endColumn="91"
            endOffset="30689"/>
    </incident>

    <incident
        id="UnprotectedSMSBroadcastReceiver"
        severity="warning"
        message="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents">
        <fix-attribute
            description="Set permission=&quot;android.permission.BROADCAST_SMS&quot;"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="permission"
            value="android.permission.BROADCAST_SMS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="117"
            column="10"
            startOffset="5326"
            endLine="117"
            endColumn="18"
            endOffset="5334"/>
    </incident>

    <incident
        id="UnprotectedSMSBroadcastReceiver"
        severity="warning"
        message="BroadcastReceivers that declare an intent-filter for `SMS_DELIVER` or `SMS_RECEIVED` must ensure that the caller has the `BROADCAST_SMS` permission, otherwise it is possible for malicious actors to spoof intents">
        <fix-attribute
            description="Set permission=&quot;android.permission.BROADCAST_SMS&quot;"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="permission"
            value="android.permission.BROADCAST_SMS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="134"
            column="10"
            startOffset="5953"
            endLine="134"
            endColumn="18"
            endOffset="5961"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="156"
            column="21"
            startOffset="5551"
            endLine="156"
            endColumn="58"
            endOffset="5588"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeSmsReceiver.java"
            line="55"
            column="21"
            startOffset="1921"
            endLine="55"
            endColumn="89"
            endOffset="1989"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="34"
            column="13"
            startOffset="1059"
            endLine="34"
            endColumn="70"
            endOffset="1116"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="55"
            column="17"
            startOffset="1766"
            endLine="55"
            endColumn="70"
            endOffset="1819"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="84"
            column="17"
            startOffset="2704"
            endLine="84"
            endColumn="70"
            endOffset="2757"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="100"
            column="29"
            startOffset="3613"
            endLine="100"
            endColumn="86"
            endOffset="3670"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="126"
            column="17"
            startOffset="4540"
            endLine="126"
            endColumn="74"
            endOffset="4597"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="147"
            column="17"
            startOffset="5175"
            endLine="147"
            endColumn="74"
            endOffset="5232"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="236"
            column="17"
            startOffset="8302"
            endLine="236"
            endColumn="74"
            endOffset="8359"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="257"
            column="17"
            startOffset="9120"
            endLine="257"
            endColumn="74"
            endOffset="9177"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/telecom/TelecomDialFunction.java"
            line="275"
            column="17"
            startOffset="9827"
            endLine="275"
            endColumn="74"
            endOffset="9884"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
            line="47"
            column="17"
            startOffset="1528"
            endLine="47"
            endColumn="92"
            endOffset="1603"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is never &lt; 24">
        <fix-data conditional="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
            line="78"
            column="17"
            startOffset="2936"
            endLine="78"
            endColumn="91"
            endOffset="3010"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
            line="227"
            column="17"
            startOffset="8033"
            endLine="227"
            endColumn="92"
            endOffset="8108"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java"
            line="250"
            column="17"
            startOffset="8938"
            endLine="250"
            endColumn="92"
            endOffset="9013"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="8"
            column="5"
            startOffset="339"
            endLine="8"
            endColumn="33"
            endOffset="367"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="7"
            column="5"
            startOffset="283"
            endLine="7"
            endColumn="33"
            endOffset="311"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="7"
            column="5"
            startOffset="283"
            endLine="7"
            endColumn="33"
            endOffset="311"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.Appy99Lisence`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="7"
            column="5"
            startOffset="283"
            endLine="7"
            endColumn="33"
            endOffset="311"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="44"
            startOffset="854"
            endLine="15"
            endColumn="71"
            endOffset="881"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="44"
            startOffset="934"
            endLine="16"
            endColumn="71"
            endOffset="961"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="10090"
                    endOffset="12611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="10374"
                    endOffset="11059"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="11089"
                    endOffset="11845"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="11875"
                    endOffset="12570"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="247"
            column="30"
            startOffset="10375"
            endLine="247"
            endColumn="36"
            endOffset="10381"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="10090"
                    endOffset="12611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="10374"
                    endOffset="11059"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="11089"
                    endOffset="11845"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="11875"
                    endOffset="12570"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="260"
            column="30"
            startOffset="11090"
            endLine="260"
            endColumn="36"
            endOffset="11096"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="10090"
                    endOffset="12611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="10374"
                    endOffset="11059"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="11089"
                    endOffset="11845"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
                    startOffset="11875"
                    endOffset="12570"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="274"
            column="30"
            startOffset="11876"
            endLine="274"
            endColumn="36"
            endOffset="11882"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="10206"
                    endOffset="11833"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="10470"
                    endOffset="11120"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="11146"
                    endOffset="11796"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="242"
            column="26"
            startOffset="10471"
            endLine="242"
            endColumn="32"
            endOffset="10477"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="10206"
                    endOffset="11833"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="10470"
                    endOffset="11120"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="11146"
                    endOffset="11796"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="255"
            column="26"
            startOffset="11147"
            endLine="255"
            endColumn="32"
            endOffset="11153"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="13527"
                    endOffset="14958"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="13802"
                    endOffset="14364"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="14382"
                    endOffset="14929"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="320"
            column="18"
            startOffset="13803"
            endLine="320"
            endColumn="24"
            endOffset="13809"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="13527"
                    endOffset="14958"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="13802"
                    endOffset="14364"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
                    startOffset="14382"
                    endOffset="14929"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="333"
            column="18"
            startOffset="14383"
            endLine="333"
            endColumn="24"
            endOffset="14389"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
                    startOffset="9510"
                    endOffset="10852"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
                    startOffset="9766"
                    endOffset="10282"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
                    startOffset="10304"
                    endOffset="10820"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="239"
            column="22"
            startOffset="9767"
            endLine="239"
            endColumn="28"
            endOffset="9773"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
                    startOffset="9510"
                    endOffset="10852"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
                    startOffset="9766"
                    endOffset="10282"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
                    startOffset="10304"
                    endOffset="10820"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="250"
            column="22"
            startOffset="10305"
            endLine="250"
            endColumn="28"
            endOffset="10311"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1142"
                    endOffset="2750"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1366"
                    endOffset="1790"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1808"
                    endOffset="2283"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="2301"
                    endOffset="2722"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="38"
            column="18"
            startOffset="1367"
            endLine="38"
            endColumn="24"
            endOffset="1373"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1142"
                    endOffset="2750"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1366"
                    endOffset="1790"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1808"
                    endOffset="2283"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="2301"
                    endOffset="2722"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="48"
            column="18"
            startOffset="1809"
            endLine="48"
            endColumn="24"
            endOffset="1815"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1142"
                    endOffset="2750"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1366"
                    endOffset="1790"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="1808"
                    endOffset="2283"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
                    startOffset="2301"
                    endOffset="2722"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="59"
            column="18"
            startOffset="2302"
            endLine="59"
            endColumn="24"
            endOffset="2308"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
                    startOffset="1764"
                    endOffset="3122"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
                    startOffset="2020"
                    endOffset="2544"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
                    startOffset="2566"
                    endOffset="3090"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="51"
            column="22"
            startOffset="2021"
            endLine="51"
            endColumn="28"
            endOffset="2027"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
                    startOffset="1764"
                    endOffset="3122"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
                    startOffset="2020"
                    endOffset="2544"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
                    startOffset="2566"
                    endOffset="3090"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="62"
            column="22"
            startOffset="2567"
            endLine="62"
            endColumn="28"
            endOffset="2573"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="81"
            column="22"
            startOffset="3243"
            endLine="81"
            endColumn="30"
            endOffset="3251"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="123"
            column="22"
            startOffset="5061"
            endLine="123"
            endColumn="30"
            endOffset="5069"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="85"
            column="22"
            startOffset="3381"
            endLine="85"
            endColumn="30"
            endOffset="3389"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="106"
            column="22"
            startOffset="4381"
            endLine="106"
            endColumn="30"
            endOffset="4389"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="157"
            column="22"
            startOffset="6624"
            endLine="157"
            endColumn="30"
            endOffset="6632"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="177"
            column="22"
            startOffset="7571"
            endLine="177"
            endColumn="30"
            endOffset="7579"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="197"
            column="22"
            startOffset="8541"
            endLine="197"
            endColumn="30"
            endOffset="8549"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="192"
            column="22"
            startOffset="8105"
            endLine="192"
            endColumn="30"
            endOffset="8113"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-cardview"
            robot="true">
            <fix-replace
                description="Replace with cardviewVersion = &quot;1.0.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cardviewVersion = &quot;1.0.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-cardview = { module = &quot;androidx.cardview:cardview&quot;, version.ref = &quot;cardviewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-cardview = { module = &quot;androidx.cardview:cardview&quot;, version.ref = &quot;cardviewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="189"
                    endOffset="189"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.cardview"
                robot="true"
                replacement="libs.androidx.cardview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="876"
                    endOffset="910"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="37"
            column="20"
            startOffset="876"
            endLine="37"
            endColumn="54"
            endOffset="910"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-recyclerview"
            robot="true">
            <fix-replace
                description="Replace with recyclerviewVersion = &quot;1.4.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="recyclerviewVersion = &quot;1.4.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="176"
                    endOffset="176"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-recyclerview = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;recyclerviewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-recyclerview = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;recyclerviewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="189"
                    endOffset="189"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.recyclerview"
                robot="true"
                replacement="libs.androidx.recyclerview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="982"
                    endOffset="1024"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="40"
            column="20"
            startOffset="982"
            endLine="40"
            endColumn="62"
            endOffset="1024"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-preference"
            robot="true">
            <fix-replace
                description="Replace with preferenceVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="preferenceVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="176"
                    endOffset="176"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-preference = { module = &quot;androidx.preference:preference&quot;, version.ref = &quot;preferenceVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-preference = { module = &quot;androidx.preference:preference&quot;, version.ref = &quot;preferenceVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="189"
                    endOffset="189"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.preference"
                robot="true"
                replacement="libs.androidx.preference"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1044"
                    endOffset="1082"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="41"
            column="20"
            startOffset="1044"
            endLine="41"
            endColumn="58"
            endOffset="1082"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="176"
                    endOffset="176"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="851"
                    endOffset="851"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.retrofit"
                robot="true"
                replacement="libs.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1102"
                    endOffset="1141"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="42"
            column="20"
            startOffset="1102"
            endLine="42"
            endColumn="59"
            endOffset="1141"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-converter-gson"
            robot="true">
            <fix-replace
                description="Replace with converterGsonVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converterGsonVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="851"
                    endOffset="851"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.converter.gson"
                robot="true"
                replacement="libs.retrofit2.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1161"
                    endOffset="1206"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="43"
            column="20"
            startOffset="1161"
            endLine="43"
            endColumn="65"
            endOffset="1206"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp3-logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with loggingInterceptorVersion = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="loggingInterceptorVersion = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="109"
                    endOffset="109"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="851"
                    endOffset="851"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp3.logging.interceptor"
                robot="true"
                replacement="libs.okhttp3.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1226"
                    endOffset="1275"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="44"
            column="20"
            startOffset="1226"
            endLine="44"
            endColumn="69"
            endOffset="1275"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="336"
            column="30"
            startOffset="14889"
            endLine="336"
            endColumn="39"
            endOffset="14898"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="369"
            column="30"
            startOffset="16707"
            endLine="369"
            endColumn="39"
            endOffset="16716"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="401"
            column="30"
            startOffset="18484"
            endLine="401"
            endColumn="39"
            endOffset="18493"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="433"
            column="30"
            startOffset="20237"
            endLine="433"
            endColumn="39"
            endOffset="20246"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="37"
            column="14"
            startOffset="1506"
            endLine="37"
            endColumn="23"
            endOffset="1515"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="70"
            column="14"
            startOffset="2820"
            endLine="70"
            endColumn="23"
            endOffset="2829"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="102"
            column="14"
            startOffset="4109"
            endLine="102"
            endColumn="23"
            endOffset="4118"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="134"
            column="14"
            startOffset="5382"
            endLine="134"
            endColumn="23"
            endOffset="5391"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="172"
            column="18"
            startOffset="6736"
            endLine="172"
            endColumn="27"
            endOffset="6745"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="205"
            column="29"
            startOffset="7237"
            endLine="205"
            endColumn="60"
            endOffset="7268"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="209"
            column="33"
            startOffset="7394"
            endLine="209"
            endColumn="63"
            endOffset="7424"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="209"
            column="33"
            startOffset="7394"
            endLine="209"
            endColumn="47"
            endOffset="7408"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="212"
            column="32"
            startOffset="7489"
            endLine="212"
            endColumn="57"
            endOffset="7514"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="212"
            column="32"
            startOffset="7489"
            endLine="212"
            endColumn="45"
            endOffset="7502"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="221"
            column="44"
            startOffset="7907"
            endLine="222"
            endColumn="81"
            endOffset="8027"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="221"
            column="44"
            startOffset="7907"
            endLine="221"
            endColumn="57"
            endOffset="7920"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="222"
            column="42"
            startOffset="7988"
            endLine="222"
            endColumn="62"
            endOffset="8008"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="224"
            column="44"
            startOffset="8094"
            endLine="224"
            endColumn="90"
            endOffset="8140"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="224"
            column="44"
            startOffset="8094"
            endLine="224"
            endColumn="73"
            endOffset="8123"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="228"
            column="40"
            startOffset="8318"
            endLine="228"
            endColumn="69"
            endOffset="8347"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="233"
            column="36"
            startOffset="8485"
            endLine="234"
            endColumn="143"
            endOffset="8663"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="233"
            column="36"
            startOffset="8485"
            endLine="233"
            endColumn="49"
            endOffset="8498"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="234"
            column="34"
            startOffset="8554"
            endLine="234"
            endColumn="50"
            endOffset="8570"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="236"
            column="36"
            startOffset="8718"
            endLine="236"
            endColumn="73"
            endOffset="8755"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="249"
            column="29"
            startOffset="9116"
            endLine="249"
            endColumn="53"
            endOffset="9140"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="250"
            column="33"
            startOffset="9175"
            endLine="250"
            endColumn="83"
            endOffset="9225"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="251"
            column="32"
            startOffset="9259"
            endLine="251"
            endColumn="81"
            endOffset="9308"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="661"
            column="55"
            startOffset="23556"
            endLine="661"
            endColumn="84"
            endOffset="23585"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="673"
            column="55"
            startOffset="24035"
            endLine="673"
            endColumn="80"
            endOffset="24060"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="683"
            column="43"
            startOffset="24425"
            endLine="683"
            endColumn="104"
            endOffset="24486"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/DashboardActivity.java"
            line="683"
            column="43"
            startOffset="24425"
            endLine="683"
            endColumn="61"
            endOffset="24443"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="188"
            column="32"
            startOffset="6689"
            endLine="188"
            endColumn="62"
            endOffset="6719"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="195"
            column="36"
            startOffset="7069"
            endLine="195"
            endColumn="76"
            endOffset="7109"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="198"
            column="36"
            startOffset="7212"
            endLine="198"
            endColumn="87"
            endOffset="7263"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="226"
            column="28"
            startOffset="8051"
            endLine="226"
            endColumn="47"
            endOffset="8070"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="396"
            column="32"
            startOffset="14661"
            endLine="396"
            endColumn="61"
            endOffset="14690"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="474"
            column="28"
            startOffset="17332"
            endLine="474"
            endColumn="51"
            endOffset="17355"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="520"
            column="44"
            startOffset="18956"
            endLine="520"
            endColumn="93"
            endOffset="19005"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="619"
            column="40"
            startOffset="22389"
            endLine="619"
            endColumn="72"
            endOffset="22421"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="658"
            column="40"
            startOffset="23868"
            endLine="658"
            endColumn="124"
            endOffset="23952"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="658"
            column="40"
            startOffset="23868"
            endLine="658"
            endColumn="69"
            endOffset="23897"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="836"
            column="32"
            startOffset="29947"
            endLine="836"
            endColumn="43"
            endOffset="29958"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="919"
            column="32"
            startOffset="32575"
            endLine="919"
            endColumn="67"
            endOffset="32610"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="969"
            column="36"
            startOffset="34451"
            endLine="969"
            endColumn="60"
            endOffset="34475"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="969"
            column="36"
            startOffset="34451"
            endLine="969"
            endColumn="51"
            endOffset="34466"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1024"
            column="51"
            startOffset="36142"
            endLine="1024"
            endColumn="66"
            endOffset="36157"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1026"
            column="51"
            startOffset="36239"
            endLine="1026"
            endColumn="71"
            endOffset="36259"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1558"
            column="40"
            startOffset="57023"
            endLine="1558"
            endColumn="57"
            endOffset="57040"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1562"
            column="39"
            startOffset="57192"
            endLine="1562"
            endColumn="66"
            endOffset="57219"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1583"
            column="36"
            startOffset="58146"
            endLine="1583"
            endColumn="72"
            endOffset="58182"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1583"
            column="36"
            startOffset="58146"
            endLine="1583"
            endColumn="55"
            endOffset="58165"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1932"
            column="44"
            startOffset="72181"
            endLine="1932"
            endColumn="62"
            endOffset="72199"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/MainActivity.java"
            line="1937"
            column="40"
            startOffset="72335"
            endLine="1937"
            endColumn="102"
            endOffset="72397"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="257"
            column="40"
            startOffset="9201"
            endLine="257"
            endColumn="71"
            endOffset="9232"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="257"
            column="40"
            startOffset="9201"
            endLine="257"
            endColumn="49"
            endOffset="9210"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="258"
            column="44"
            startOffset="9278"
            endLine="258"
            endColumn="75"
            endOffset="9309"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="259"
            column="39"
            startOffset="9350"
            endLine="259"
            endColumn="74"
            endOffset="9385"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="259"
            column="68"
            startOffset="9379"
            endLine="259"
            endColumn="74"
            endOffset="9385"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="260"
            column="41"
            startOffset="9428"
            endLine="260"
            endColumn="69"
            endOffset="9456"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="261"
            column="39"
            startOffset="9497"
            endLine="261"
            endColumn="70"
            endOffset="9528"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="261"
            column="39"
            startOffset="9497"
            endLine="261"
            endColumn="49"
            endOffset="9507"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java"
            line="262"
            column="37"
            startOffset="9567"
            endLine="262"
            endColumn="66"
            endOffset="9596"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🎉 Welcome to Your Dashboard!&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="37"
            column="21"
            startOffset="1360"
            endLine="37"
            endColumn="65"
            endOffset="1404"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Your license and domain information&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="47"
            column="21"
            startOffset="1804"
            endLine="47"
            endColumn="71"
            endOffset="1854"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📄 License Information&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="71"
            column="25"
            startOffset="2767"
            endLine="71"
            endColumn="62"
            endOffset="2804"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="81"
            column="25"
            startOffset="3250"
            endLine="81"
            endColumn="59"
            endOffset="3284"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🌐 Domain Access&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="107"
            column="25"
            startOffset="4271"
            endLine="107"
            endColumn="56"
            endOffset="4302"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Domain: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="117"
            column="25"
            startOffset="4747"
            endLine="117"
            endColumn="58"
            endOffset="4780"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⏰ License Expiration&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="143"
            column="25"
            startOffset="5771"
            endLine="143"
            endColumn="60"
            endOffset="5806"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Expiration: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="153"
            column="25"
            startOffset="6255"
            endLine="153"
            endColumn="62"
            endOffset="6292"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📱 Device Information&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="179"
            column="25"
            startOffset="7279"
            endLine="179"
            endColumn="61"
            endOffset="7315"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Device: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="189"
            column="25"
            startOffset="7760"
            endLine="189"
            endColumn="58"
            endOffset="7793"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📱 Mobile Recharge Module&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="225"
            column="29"
            startOffset="9263"
            endLine="225"
            endColumn="69"
            endOffset="9303"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔴 Recharge service is stopped&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="235"
            column="29"
            startOffset="9788"
            endLine="235"
            endColumn="74"
            endOffset="9833"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💳 Recharge&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="253"
            column="33"
            startOffset="10720"
            endLine="253"
            endColumn="59"
            endOffset="10746"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📊 History&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="267"
            column="33"
            startOffset="11507"
            endLine="267"
            endColumn="58"
            endOffset="11532"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⚙️ Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="280"
            column="33"
            startOffset="12231"
            endLine="280"
            endColumn="59"
            endOffset="12257"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="346"
            column="33"
            startOffset="15422"
            endLine="346"
            endColumn="52"
            endOffset="15441"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="379"
            column="33"
            startOffset="17249"
            endLine="379"
            endColumn="55"
            endOffset="17271"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="411"
            column="33"
            startOffset="19021"
            endLine="411"
            endColumn="56"
            endOffset="19044"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="443"
            column="33"
            startOffset="20779"
            endLine="443"
            endColumn="55"
            endOffset="20801"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔙 Back to Main&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="464"
            column="17"
            startOffset="21635"
            endLine="464"
            endColumn="47"
            endOffset="21665"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🎛️ Dashboard - License Management System&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="487"
            column="13"
            startOffset="22380"
            endLine="487"
            endColumn="69"
            endOffset="22436"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Activation&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="38"
            column="21"
            startOffset="1396"
            endLine="38"
            endColumn="54"
            endOffset="1429"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your license key and PIN to activate&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="48"
            column="21"
            startOffset="1843"
            endLine="48"
            endColumn="78"
            endOffset="1900"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Key&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="74"
            column="25"
            startOffset="2920"
            endLine="74"
            endColumn="51"
            endOffset="2946"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your license key&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="85"
            column="25"
            startOffset="3447"
            endLine="85"
            endColumn="62"
            endOffset="3484"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;PIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="116"
            column="25"
            startOffset="4746"
            endLine="116"
            endColumn="43"
            endOffset="4764"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your PIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="127"
            column="25"
            startOffset="5258"
            endLine="127"
            endColumn="54"
            endOffset="5287"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Activate License&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="145"
            column="17"
            startOffset="5995"
            endLine="145"
            endColumn="48"
            endOffset="6026"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Please enter your license key and PIN to activate&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="160"
            column="17"
            startOffset="6639"
            endLine="160"
            endColumn="81"
            endOffset="6703"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🌐&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="202"
            column="33"
            startOffset="8585"
            endLine="202"
            endColumn="50"
            endOffset="8602"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Domain Access&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="209"
            column="33"
            startOffset="8933"
            endLine="209"
            endColumn="61"
            endOffset="8961"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Your domain: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="221"
            column="29"
            startOffset="9503"
            endLine="221"
            endColumn="67"
            endOffset="9541"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🎛️ Open Dashboard&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="231"
            column="29"
            startOffset="10022"
            endLine="231"
            endColumn="62"
            endOffset="10055"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💡 View your license details and domain information&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="244"
            column="29"
            startOffset="10734"
            endLine="244"
            endColumn="95"
            endOffset="10800"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📅&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="288"
            column="33"
            startOffset="12658"
            endLine="288"
            endColumn="50"
            endOffset="12675"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="295"
            column="33"
            startOffset="13006"
            endLine="295"
            endColumn="62"
            endOffset="13035"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License expires: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="307"
            column="29"
            startOffset="13577"
            endLine="307"
            endColumn="71"
            endOffset="13619"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Time remaining: Calculating...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="317"
            column="29"
            startOffset="14106"
            endLine="317"
            endColumn="74"
            endOffset="14151"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💡 You will receive warnings 2 days before expiration&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="337"
            column="29"
            startOffset="15187"
            endLine="337"
            endColumn="97"
            endOffset="15255"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👨‍💻&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="374"
            column="17"
            startOffset="16444"
            endLine="374"
            endColumn="37"
            endOffset="16464"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Md Sadrul Hasan Dider&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="381"
            column="17"
            startOffset="16699"
            endLine="381"
            endColumn="53"
            endOffset="16735"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;© 2025 License Activation System&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="392"
            column="13"
            startOffset="17072"
            endLine="392"
            endColumn="60"
            endOffset="17119"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📱 Mobile Recharge&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="35"
            column="21"
            startOffset="1262"
            endLine="35"
            endColumn="54"
            endOffset="1295"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Submit mobile recharge requests&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="45"
            column="21"
            startOffset="1695"
            endLine="45"
            endColumn="67"
            endOffset="1741"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💳 Recharge Details&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="69"
            column="25"
            startOffset="2648"
            endLine="69"
            endColumn="59"
            endOffset="2682"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📞 Phone Number&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="79"
            column="25"
            startOffset="3118"
            endLine="79"
            endColumn="55"
            endOffset="3148"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter phone number&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="90"
            column="25"
            startOffset="3645"
            endLine="90"
            endColumn="58"
            endOffset="3678"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💰 Amount (BDT)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="100"
            column="25"
            startOffset="4118"
            endLine="100"
            endColumn="55"
            endOffset="4148"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="111"
            column="25"
            startOffset="4640"
            endLine="111"
            endColumn="52"
            endOffset="4667"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📡 Select Operator&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="121"
            column="25"
            startOffset="5121"
            endLine="121"
            endColumn="58"
            endOffset="5154"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📱 Select SIM Slot&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="139"
            column="25"
            startOffset="5973"
            endLine="139"
            endColumn="58"
            endOffset="6006"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚀 Submit Recharge&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="158"
            column="25"
            startOffset="6872"
            endLine="158"
            endColumn="58"
            endOffset="6905"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📊 Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="186"
            column="25"
            startOffset="7992"
            endLine="186"
            endColumn="49"
            endOffset="8016"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Ready to submit recharge&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="196"
            column="25"
            startOffset="8457"
            endLine="196"
            endColumn="64"
            endOffset="8496"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;ℹ️ Instructions&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="224"
            column="25"
            startOffset="9564"
            endLine="224"
            endColumn="55"
            endOffset="9594"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;1. Enter the phone number to recharge\n2. Enter the recharge amount\n3. Select the mobile operator\n4. Choose the SIM slot to use\n5. Click Submit to process the recharge\n\nThe app will automatically dial the USSD code for you.&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="233"
            column="25"
            startOffset="9982"
            endLine="233"
            endColumn="268"
            endOffset="10225"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔙 Back to Dashboard&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="247"
            column="17"
            startOffset="10679"
            endLine="247"
            endColumn="52"
            endOffset="10714"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📱 Mobile Recharge Module - License Management System&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge.xml"
            line="270"
            column="13"
            startOffset="11402"
            endLine="270"
            endColumn="81"
            endOffset="11470"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📊 Recharge History&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="23"
            column="13"
            startOffset="804"
            endLine="23"
            endColumn="47"
            endOffset="838"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔄 Refresh&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="32"
            column="13"
            startOffset="1117"
            endLine="32"
            endColumn="38"
            endOffset="1142"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Loading recharge history...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="45"
            column="9"
            startOffset="1521"
            endLine="45"
            endColumn="51"
            endOffset="1563"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📭&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="76"
            column="13"
            startOffset="2554"
            endLine="76"
            endColumn="30"
            endOffset="2571"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No recharge history found&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="83"
            column="13"
            startOffset="2786"
            endLine="83"
            endColumn="53"
            endOffset="2826"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Your recharge transactions will appear here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="92"
            column="13"
            startOffset="3117"
            endLine="92"
            endColumn="71"
            endOffset="3175"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔙 Back to Dashboard&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_history.xml"
            line="111"
            column="13"
            startOffset="3725"
            endLine="111"
            endColumn="48"
            endOffset="3760"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⚙️ Recharge Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="35"
            column="21"
            startOffset="1270"
            endLine="35"
            endColumn="56"
            endOffset="1305"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Configure your recharge preferences&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="45"
            column="21"
            startOffset="1705"
            endLine="45"
            endColumn="71"
            endOffset="1755"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📱 SIM Configuration&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="69"
            column="25"
            startOffset="2666"
            endLine="69"
            endColumn="60"
            endOffset="2701"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📡 SIM 1 Operator&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="79"
            column="25"
            startOffset="3133"
            endLine="79"
            endColumn="57"
            endOffset="3165"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📡 SIM 2 Operator&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="97"
            column="25"
            startOffset="3984"
            endLine="97"
            endColumn="57"
            endOffset="4016"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔄 Enable automatic recharge processing&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="116"
            column="25"
            startOffset="4906"
            endLine="116"
            endColumn="79"
            endOffset="4960"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🌐 Server Configuration&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="141"
            column="25"
            startOffset="5891"
            endLine="141"
            endColumn="63"
            endOffset="5929"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔗 Server URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="151"
            column="25"
            startOffset="6363"
            endLine="151"
            endColumn="53"
            endOffset="6391"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter server URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="162"
            column="25"
            startOffset="6886"
            endLine="162"
            endColumn="56"
            endOffset="6917"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔑 API PIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="171"
            column="25"
            startOffset="7313"
            endLine="171"
            endColumn="50"
            endOffset="7338"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter API PIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="182"
            column="25"
            startOffset="7830"
            endLine="182"
            endColumn="53"
            endOffset="7858"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⏱️ Check Interval (seconds)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="191"
            column="25"
            startOffset="8266"
            endLine="191"
            endColumn="67"
            endOffset="8308"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Check interval in seconds&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="202"
            column="25"
            startOffset="8807"
            endLine="202"
            endColumn="65"
            endOffset="8847"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;5&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="204"
            column="25"
            startOffset="8923"
            endLine="204"
            endColumn="41"
            endOffset="8939"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🎛️ Service Control&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="229"
            column="25"
            startOffset="9886"
            endLine="229"
            endColumn="59"
            endOffset="9920"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;▶️ Start Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="248"
            column="29"
            startOffset="10796"
            endLine="248"
            endColumn="60"
            endOffset="10827"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⏹️ Stop Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="261"
            column="29"
            startOffset="11473"
            endLine="261"
            endColumn="59"
            endOffset="11503"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📊 Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="291"
            column="25"
            startOffset="12647"
            endLine="291"
            endColumn="49"
            endOffset="12671"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Loading settings...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="301"
            column="25"
            startOffset="13112"
            endLine="301"
            endColumn="59"
            endOffset="13146"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💾 Save Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="326"
            column="21"
            startOffset="14080"
            endLine="326"
            endColumn="52"
            endOffset="14111"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔙 Back&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="339"
            column="21"
            startOffset="14654"
            endLine="339"
            endColumn="43"
            endOffset="14676"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⚙️ Recharge Settings - License Management System&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recharge_settings.xml"
            line="364"
            column="13"
            startOffset="15413"
            endLine="364"
            endColumn="76"
            endOffset="15476"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="47"
            column="17"
            startOffset="1895"
            endLine="47"
            endColumn="36"
            endOffset="1914"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="80"
            column="17"
            startOffset="3218"
            endLine="80"
            endColumn="39"
            endOffset="3240"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="112"
            column="17"
            startOffset="4502"
            endLine="112"
            endColumn="40"
            endOffset="4525"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="144"
            column="17"
            startOffset="5780"
            endLine="144"
            endColumn="39"
            endOffset="5802"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Automatic Mobile Recharge System&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="185"
            column="21"
            startOffset="7330"
            endLine="185"
            endColumn="68"
            endOffset="7377"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Device ID: Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="199"
            column="21"
            startOffset="8049"
            endLine="199"
            endColumn="57"
            endOffset="8085"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Service Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="219"
            column="21"
            startOffset="8913"
            endLine="219"
            endColumn="50"
            endOffset="8942"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Service Status: Checking...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="229"
            column="21"
            startOffset="9355"
            endLine="229"
            endColumn="63"
            endOffset="9397"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="246"
            column="25"
            startOffset="10151"
            endLine="246"
            endColumn="53"
            endOffset="10179"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Stop Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="257"
            column="25"
            startOffset="10690"
            endLine="257"
            endColumn="52"
            endOffset="10717"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Network Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="277"
            column="21"
            startOffset="11498"
            endLine="277"
            endColumn="52"
            endOffset="11529"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SIM A:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="295"
            column="25"
            startOffset="12317"
            endLine="295"
            endColumn="46"
            endOffset="12338"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SIM B:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="321"
            column="25"
            startOffset="13503"
            endLine="321"
            endColumn="46"
            endOffset="13524"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;System Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="350"
            column="21"
            startOffset="14741"
            endLine="350"
            endColumn="51"
            endOffset="14771"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SMS Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="368"
            column="25"
            startOffset="15556"
            endLine="368"
            endColumn="51"
            endOffset="15582"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Accessibility&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="398"
            column="25"
            startOffset="16983"
            endLine="398"
            endColumn="53"
            endOffset="17011"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Power Optimize&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_telecom_dashboard.xml"
            line="427"
            column="25"
            startOffset="18362"
            endLine="427"
            endColumn="54"
            endOffset="18391"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="6"
            column="9"
            startOffset="206"
            endLine="6"
            endColumn="29"
            endOffset="226"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;M Banking&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="10"
            column="9"
            startOffset="340"
            endLine="10"
            endColumn="34"
            endOffset="365"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="14"
            column="9"
            startOffset="473"
            endLine="14"
            endColumn="33"
            endOffset="497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Monitor&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="18"
            column="9"
            startOffset="610"
            endLine="18"
            endColumn="32"
            endOffset="633"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mobile Banking Services&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="30"
            column="21"
            startOffset="1103"
            endLine="30"
            endColumn="59"
            endOffset="1141"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;bKash&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="49"
            column="25"
            startOffset="1949"
            endLine="49"
            endColumn="45"
            endOffset="1969"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Rocket&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="78"
            column="25"
            startOffset="3216"
            endLine="78"
            endColumn="46"
            endOffset="3237"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Nagad&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="106"
            column="25"
            startOffset="4429"
            endLine="106"
            endColumn="45"
            endOffset="4449"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking Configuration&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="142"
            column="21"
            startOffset="5891"
            endLine="142"
            endColumn="57"
            endOffset="5927"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Auto Banking&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="161"
            column="25"
            startOffset="6742"
            endLine="161"
            endColumn="52"
            endOffset="6769"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking Delay (seconds)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="187"
            column="25"
            startOffset="7883"
            endLine="187"
            endColumn="63"
            endOffset="7921"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter delay in seconds&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="197"
            column="25"
            startOffset="8393"
            endLine="197"
            endColumn="62"
            endOffset="8430"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;5&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="200"
            column="25"
            startOffset="8553"
            endLine="200"
            endColumn="41"
            endOffset="8569"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="222"
            column="21"
            startOffset="9359"
            endLine="222"
            endColumn="50"
            endOffset="9388"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking services are ready&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="232"
            column="21"
            startOffset="9802"
            endLine="232"
            endColumn="62"
            endOffset="9843"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Banking Connection&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_banking.xml"
            line="241"
            column="21"
            startOffset="10202"
            endLine="241"
            endColumn="59"
            endOffset="10240"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Total Recharges&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_home.xml"
            line="36"
            column="21"
            startOffset="1309"
            endLine="36"
            endColumn="51"
            endOffset="1339"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_home.xml"
            line="45"
            column="21"
            startOffset="1709"
            endLine="45"
            endColumn="37"
            endOffset="1725"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Success Rate&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_home.xml"
            line="72"
            column="21"
            startOffset="2738"
            endLine="72"
            endColumn="48"
            endOffset="2765"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_home.xml"
            line="81"
            column="21"
            startOffset="3137"
            endLine="81"
            endColumn="38"
            endOffset="3154"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Recent Activity&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_home.xml"
            line="107"
            column="17"
            startOffset="4031"
            endLine="107"
            endColumn="47"
            endOffset="4061"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No recent activity&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_home.xml"
            line="122"
            column="17"
            startOffset="4659"
            endLine="122"
            endColumn="50"
            endOffset="4692"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Monitor Controls&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="26"
            column="17"
            startOffset="918"
            endLine="26"
            endColumn="48"
            endOffset="949"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Refresh&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="44"
            column="21"
            startOffset="1648"
            endLine="44"
            endColumn="43"
            endOffset="1670"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Clear Logs&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="55"
            column="21"
            startOffset="2138"
            endLine="55"
            endColumn="46"
            endOffset="2163"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Export&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="65"
            column="21"
            startOffset="2581"
            endLine="65"
            endColumn="42"
            endOffset="2602"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Live Monitor&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="97"
            column="21"
            startOffset="3742"
            endLine="97"
            endColumn="48"
            endOffset="3769"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🟢 Active&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="106"
            column="21"
            startOffset="4133"
            endLine="106"
            endColumn="45"
            endOffset="4157"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Auto Scroll&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="140"
            column="21"
            startOffset="5537"
            endLine="140"
            endColumn="47"
            endOffset="5563"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No logs available&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_monitor.xml"
            line="161"
            column="17"
            startOffset="6386"
            endLine="161"
            endColumn="49"
            endOffset="6418"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Service Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="30"
            column="21"
            startOffset="1103"
            endLine="30"
            endColumn="50"
            endOffset="1132"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Service Status: Checking...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="40"
            column="21"
            startOffset="1554"
            endLine="40"
            endColumn="63"
            endOffset="1596"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="58"
            column="25"
            startOffset="2413"
            endLine="58"
            endColumn="53"
            endOffset="2441"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Stop Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="69"
            column="25"
            startOffset="2960"
            endLine="69"
            endColumn="52"
            endOffset="2987"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Network Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="94"
            column="21"
            startOffset="3926"
            endLine="94"
            endColumn="52"
            endOffset="3957"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SIM A:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="113"
            column="25"
            startOffset="4802"
            endLine="113"
            endColumn="46"
            endOffset="4823"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SIM B:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="137"
            column="25"
            startOffset="5884"
            endLine="137"
            endColumn="46"
            endOffset="5905"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;System Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="168"
            column="21"
            startOffset="7127"
            endLine="168"
            endColumn="51"
            endOffset="7157"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SMS Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="188"
            column="25"
            startOffset="8025"
            endLine="188"
            endColumn="51"
            endOffset="8051"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Accessibility&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="217"
            column="25"
            startOffset="9362"
            endLine="217"
            endColumn="53"
            endOffset="9390"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Power Optimize&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="246"
            column="25"
            startOffset="10704"
            endLine="246"
            endColumn="54"
            endOffset="10733"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Auto Start Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_telecom_settings.xml"
            line="274"
            column="25"
            startOffset="11997"
            endLine="274"
            endColumn="58"
            endOffset="12030"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Order: RCH_123456&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recharge_history.xml"
            line="28"
            column="17"
            startOffset="1017"
            endLine="28"
            endColumn="49"
            endOffset="1049"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Completed&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recharge_history.xml"
            line="37"
            column="17"
            startOffset="1367"
            endLine="37"
            endColumn="41"
            endOffset="1391"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📞 +8801712345678&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recharge_history.xml"
            line="62"
            column="17"
            startOffset="2281"
            endLine="62"
            endColumn="49"
            endOffset="2313"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💰 50 BDT&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recharge_history.xml"
            line="70"
            column="17"
            startOffset="2590"
            endLine="70"
            endColumn="41"
            endOffset="2614"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📡 Grameenphone&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recharge_history.xml"
            line="88"
            column="17"
            startOffset="3197"
            endLine="88"
            endColumn="47"
            endOffset="3227"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📅 2024-01-15 10:30&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recharge_history.xml"
            line="96"
            column="17"
            startOffset="3502"
            endLine="96"
            endColumn="51"
            endOffset="3536"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SMS: Recharge successful&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recharge_history.xml"
            line="107"
            column="13"
            startOffset="3868"
            endLine="107"
            endColumn="52"
            endOffset="3907"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;12:34:56&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_telecom_log.xml"
            line="14"
            column="9"
            startOffset="499"
            endLine="14"
            endColumn="32"
            endOffset="522"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Log message&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_telecom_log.xml"
            line="25"
            column="9"
            startOffset="845"
            endLine="25"
            endColumn="35"
            endOffset="871"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_telecom_log.xml"
            line="34"
            column="9"
            startOffset="1136"
            endLine="34"
            endColumn="30"
            endOffset="1157"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mobile Banking&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="6"
            column="9"
            startOffset="245"
            endLine="6"
            endColumn="39"
            endOffset="275"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Forward&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="11"
            column="9"
            startOffset="401"
            endLine="11"
            endColumn="32"
            endOffset="424"/>
    </incident>

</incidents>
