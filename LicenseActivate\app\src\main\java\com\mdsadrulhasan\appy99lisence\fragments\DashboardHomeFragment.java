package com.mdsadrulhasan.appy99lisence.fragments;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.appy99lisence.R;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomLogAdapter;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomDbHelper;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DashboardHomeFragment extends Fragment {

    private TextView totalRechargesCount;
    private TextView successRatePercentage;
    private RecyclerView recentActivityRecycler;

    // Data from parent activity
    private String licenseKey;
    private String domainUrl;
    private boolean activationStatus;
    private long expirationTimestamp;
    private String deviceId;
    private String deviceInfo;

    private SharedPreferences preferences;
    private TelecomDbHelper dbHelper;
    private TelecomLogAdapter logAdapter;

    public static DashboardHomeFragment newInstance(String licenseKey, String domainUrl,
                                                   boolean activationStatus, long expirationTimestamp,
                                                   String deviceId, String deviceInfo) {
        DashboardHomeFragment fragment = new DashboardHomeFragment();
        Bundle args = new Bundle();
        args.putString("license_key", licenseKey);
        args.putString("domain_url", domainUrl);
        args.putBoolean("activation_status", activationStatus);
        args.putLong("expiration_timestamp", expirationTimestamp);
        args.putString("device_id", deviceId);
        args.putString("device_info", deviceInfo);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            licenseKey = getArguments().getString("license_key");
            domainUrl = getArguments().getString("domain_url");
            activationStatus = getArguments().getBoolean("activation_status");
            expirationTimestamp = getArguments().getLong("expiration_timestamp");
            deviceId = getArguments().getString("device_id");
            deviceInfo = getArguments().getString("device_info");
        }

        preferences = PreferenceManager.getDefaultSharedPreferences(requireContext());
        dbHelper = new TelecomDbHelper(requireContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_telecom_home, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initializeViews(view);
        setupRecyclerView();
        loadDashboardData();
    }

    private void initializeViews(View view) {
        totalRechargesCount = view.findViewById(R.id.total_recharges_count);
        successRatePercentage = view.findViewById(R.id.success_rate_percentage);
        recentActivityRecycler = view.findViewById(R.id.recent_activity_recycler);
    }

    private void setupRecyclerView() {
        if (recentActivityRecycler != null) {
            recentActivityRecycler.setLayoutManager(new LinearLayoutManager(requireContext()));
            logAdapter = new TelecomLogAdapter(requireContext());
            recentActivityRecycler.setAdapter(logAdapter);
        }
    }

    private void loadDashboardData() {
        // Load statistics
        loadStatistics();

        // Load recent activity
        loadRecentActivity();
    }

    private void loadStatistics() {
        // Get recharge statistics from preferences
        int totalRecharges = preferences.getInt("total_recharges", 0);
        int successfulRecharges = preferences.getInt("successful_recharges", 0);

        if (totalRechargesCount != null) {
            totalRechargesCount.setText(String.valueOf(totalRecharges));
        }

        if (successRatePercentage != null) {
            int successRate = totalRecharges > 0 ? (successfulRecharges * 100) / totalRecharges : 0;
            successRatePercentage.setText(successRate + "%");
        }
    }

    private void loadRecentActivity() {
        if (logAdapter != null && dbHelper != null) {
            // Load recent logs from database
            logAdapter.updateLogs(dbHelper.getRecentLogs(10));
        }
    }

    /**
     * Mask license key for security (show only first and last few characters)
     */
    private String maskLicenseKey(String license) {
        if (license == null || license.length() <= 8) {
            return license;
        }

        int start = Math.min(4, license.length() / 3);
        int end = Math.max(license.length() - 4, license.length() * 2 / 3);

        StringBuilder masked = new StringBuilder();
        masked.append(license.substring(0, start));
        for (int i = start; i < end; i++) {
            masked.append("*");
        }
        masked.append(license.substring(end));

        return masked.toString();
    }

    /**
     * Format timestamp to readable date string
     */
    private String formatTimestamp(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy 'at' hh:mm:ss a", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }

    /**
     * Format time remaining to readable string
     */
    private String formatTimeRemaining(long timeRemaining) {
        long days = timeRemaining / (24 * 60 * 60 * 1000);
        long hours = (timeRemaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
        long minutes = (timeRemaining % (60 * 60 * 1000)) / (60 * 1000);

        if (days > 0) {
            return String.format(Locale.getDefault(), "%d days, %d hours", days, hours);
        } else if (hours > 0) {
            return String.format(Locale.getDefault(), "%d hours, %d minutes", hours, minutes);
        } else {
            return String.format(Locale.getDefault(), "%d minutes", minutes);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh data when fragment becomes visible
        loadDashboardData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (dbHelper != null) {
            dbHelper.close();
        }
    }
}
